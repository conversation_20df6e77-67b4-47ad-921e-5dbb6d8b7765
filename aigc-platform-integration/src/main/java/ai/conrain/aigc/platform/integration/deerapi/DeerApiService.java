package ai.conrain.aigc.platform.integration.deerapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.gpt.AIModel.ApiConfig;
import ai.conrain.aigc.platform.integration.gpt.AIModel.GptResponse;
import ai.conrain.aigc.platform.integration.gpt.AIModel.Status;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import static ai.conrain.aigc.platform.integration.gpt.AIModel.createImageContent;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.createTextContent;

@Slf4j
@Service
public class DeerApiService {

    private static final ApiConfig api = new ApiConfig(null, "sk-oxJh2MZywKKIuLKs6xWDeKtQL0jYnb0PQ6JdePpuf2Ofbanf",
        "https://api2.deerapi.com/v1/chat/completions");

    @Autowired
    @Qualifier("superLongRestTemplate")
    private RestTemplate extraLongRestTemplate;

    /**
     * 调用大模型
     *
     * @param model   模型类型
     * @param prompt  prompt
     * @param imgUrls 图片列表
     * @return 返回结果
     */
    public GptResponse call(DeerModelEnum model, String prompt, List<String> imgUrls) {

        if (StringUtils.isEmpty(prompt) || model == null) {
            throw new RuntimeException("prompt is empty");
        }

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", String.format("Bearer %s", api.getApiKey()));

        // 构建消息体
        JSONObject payload = new JSONObject();
        payload.put("model", model.getCode());

        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("role", "user");

        JSONArray content = new JSONArray();
        content.add(createTextContent(prompt));

        if (!CollectionUtils.isEmpty(imgUrls)) {
            for (String url : imgUrls) {
                content.add(createImageContent(url));
            }
        }

        message.put("content", content);
        messages.add(message);
        payload.put("messages", messages);

        try {
            log.info("【请求GPT】，payload={}", payload);
            HttpEntity<String> entity = new HttpEntity<>(payload.toJSONString(), headers);
            ResponseEntity<String> response = extraLongRestTemplate.exchange(api.getEndpoint(), HttpMethod.POST, entity,
                String.class);

            JSONObject result = JSON.parseObject(response.getBody());

            log.info("【请求GPT】，result={}", result);

            String text = null;
            if (result != null) {
                text = result.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
            }

            return new GptResponse(text, Status.OK);
        } catch (Exception e) {
            log.error("Failed to make the request.", e);
            String errorMsg = "Failed to make the request. Error: " + e.getMessage();
            return new GptResponse(errorMsg, Status.ERROR);
        }
    }
}