/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.deerapi;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * deerAPI的模型枚举
 *
 * <AUTHOR>
 * @version : DeerModelEnum.java, v 0.1 2025/8/6 19:46 renxiao.wu Exp $
 */
@Getter
public enum DeerModelEnum {
    GPT_4_1("gpt-4.1"),

    GPT_5_NANO("gpt-5-nano"),

    GPT_5_MINI("gpt-5-mini"),

    GPT_5("gpt-5"),

    GEMINI_2_5_PRO("gemini-2.5-pro-preview-06-05"),
    ;

    /** 枚举码 */
    private final String code;

    private DeerModelEnum(String code) {
        this.code = code;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static DeerModelEnum getByCode(String code, DeerModelEnum defaultEnum) {
        if (StringUtils.isBlank(code)) {
            return defaultEnum;
        }

        for (DeerModelEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return defaultEnum;
    }
}
