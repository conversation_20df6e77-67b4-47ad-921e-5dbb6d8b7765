package ai.conrain.aigc.platform.integration.ai.model;

import lombok.Data;

@Data
public class StyleImageEmbeddingResult {
    private String id;
    private String status;
    private Embeddings embeddings;

    @Data
    public static class Embeddings {
        private float[] backgroundImageEmbedding;
        private float[] backgroundTextEmbedding;
        private float[] expressionImageEmbedding;
        private float[] expressionTextEmbedding;
        private float[] matTextEmbedding;
        private float[] optImageEmbedding;
        private float[] poseImageEmbedding;
        private float[] poseTextEmbedding;
    }
}
