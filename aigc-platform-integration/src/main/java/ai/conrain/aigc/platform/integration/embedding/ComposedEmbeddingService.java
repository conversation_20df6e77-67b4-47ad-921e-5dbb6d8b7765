package ai.conrain.aigc.platform.integration.embedding;

import ai.conrain.aigc.platform.integration.aliyun.AliyunEmbeddingService;
import ai.conrain.aigc.platform.integration.bytedance.ByteDanceEmbeddingService;
import com.pgvector.PGvector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 组合的向量服务
 * 纯文本的用阿里云的接口，可以指定不同的维度，字节是2056维向量，不能指定维度x
 * 图像的用字节的接口，返回1024维向量，限流比较高（15000/min）
 */
@Slf4j
@Service
public class ComposedEmbeddingService implements EmbeddingService {
    @Autowired
    private AliyunEmbeddingService aliyunEmbeddingService;

    @Autowired
    private ByteDanceEmbeddingService byteDanceEmbeddingService;

    /**
     * 获取单个文本的向量
     *
     * @param text      文本内容
     * @param dimension 向量维度
     * @return 向量结果
     */
    @Override
    public PGvector getEmbeddingByText(String text, int dimension) {
        return aliyunEmbeddingService.getEmbeddingByText(text, dimension);
    }

    /**
     * 获取多个文本的向量列表
     *
     * @param texts     文本列表
     * @param dimension 向量维度
     * @return 向量结果列表
     */
    @Override
    public List<PGvector> getEmbeddingByTexts(List<String> texts, int dimension) {
        return aliyunEmbeddingService.getEmbeddingByTexts(texts, dimension);
    }

    /**
     * 多模态embedding计算文本向量
     *
     * @param text 文本内容
     * @return 向量结果
     */
    @Override
    public PGvector getTextEmbeddingByMultiModalModel(String text) {
        return byteDanceEmbeddingService.getTextEmbeddingByMultiModalModel(text);
    }

    /**
     * 多模态embedding计算图像向量
     *
     * @param imgUrl 图像URL
     * @return 向量结果
     */
    @Override
    public PGvector getImgEmbeddingByMultiModalModel(String imgUrl) {
        return byteDanceEmbeddingService.getImgEmbeddingByMultiModalModel(imgUrl);
    }

    /**
     * 多模态向量化处理
     *
     * @param inputContents 输入内容列表，每个元素包含类型和数据
     * @return 向量结果列表
     */
    @Override
    public List<PGvector> getEmbeddingsByMultiModalModel(List<Pair<String, String>> inputContents) {
        return byteDanceEmbeddingService.getEmbeddingsByMultiModalModel(inputContents);
    }
}
