/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.gpt;

import com.alibaba.fastjson.JSONObject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version : AIModel.java, v 0.1 2025/8/6 20:04 renxiao.wu Exp $
 */
public class AIModel {

    public enum Status {
        OK, ERROR
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GptResponse {
        private String text;
        private Status status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApiConfig {
        private String name;
        private String apiKey;
        private String endpoint;
    }

    public static JSONObject createTextContent(String text) {
        JSONObject content = new JSONObject();
        content.put("type", "text");
        content.put("text", text);
        return content;
    }

    public static JSONObject createImageContent(String imgUrl) {
        JSONObject imageUrlJson = new JSONObject();
        imageUrlJson.put("url", imgUrl);

        JSONObject content = new JSONObject();
        content.put("type", "image_url");
        content.put("image_url", imageUrlJson);
        return content;
    }
}
