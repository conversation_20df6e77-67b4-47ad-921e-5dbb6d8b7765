package ai.conrain.aigc.platform.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CreativeBatchElementsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public CreativeBatchElementsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public CreativeBatchElementsExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public CreativeBatchElementsExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public CreativeBatchElementsExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria)this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria)this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andBatchIdIsNull() {
            addCriterion("batch_id is null");
            return (Criteria)this;
        }

        public Criteria andBatchIdIsNotNull() {
            addCriterion("batch_id is not null");
            return (Criteria)this;
        }

        public Criteria andBatchIdEqualTo(Integer value) {
            addCriterion("batch_id =", value, "batchId");
            return (Criteria)this;
        }

        public Criteria andBatchIdNotEqualTo(Integer value) {
            addCriterion("batch_id <>", value, "batchId");
            return (Criteria)this;
        }

        public Criteria andBatchIdGreaterThan(Integer value) {
            addCriterion("batch_id >", value, "batchId");
            return (Criteria)this;
        }

        public Criteria andBatchIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("batch_id >=", value, "batchId");
            return (Criteria)this;
        }

        public Criteria andBatchIdLessThan(Integer value) {
            addCriterion("batch_id <", value, "batchId");
            return (Criteria)this;
        }

        public Criteria andBatchIdLessThanOrEqualTo(Integer value) {
            addCriterion("batch_id <=", value, "batchId");
            return (Criteria)this;
        }

        public Criteria andBatchIdIn(List<Integer> values) {
            addCriterion("batch_id in", values, "batchId");
            return (Criteria)this;
        }

        public Criteria andBatchIdNotIn(List<Integer> values) {
            addCriterion("batch_id not in", values, "batchId");
            return (Criteria)this;
        }

        public Criteria andBatchIdBetween(Integer value1, Integer value2) {
            addCriterion("batch_id between", value1, value2, "batchId");
            return (Criteria)this;
        }

        public Criteria andBatchIdNotBetween(Integer value1, Integer value2) {
            addCriterion("batch_id not between", value1, value2, "batchId");
            return (Criteria)this;
        }

        public Criteria andElementIdIsNull() {
            addCriterion("element_id is null");
            return (Criteria)this;
        }

        public Criteria andElementIdIsNotNull() {
            addCriterion("element_id is not null");
            return (Criteria)this;
        }

        public Criteria andElementIdEqualTo(Integer value) {
            addCriterion("element_id =", value, "elementId");
            return (Criteria)this;
        }

        public Criteria andElementIdNotEqualTo(Integer value) {
            addCriterion("element_id <>", value, "elementId");
            return (Criteria)this;
        }

        public Criteria andElementIdGreaterThan(Integer value) {
            addCriterion("element_id >", value, "elementId");
            return (Criteria)this;
        }

        public Criteria andElementIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("element_id >=", value, "elementId");
            return (Criteria)this;
        }

        public Criteria andElementIdLessThan(Integer value) {
            addCriterion("element_id <", value, "elementId");
            return (Criteria)this;
        }

        public Criteria andElementIdLessThanOrEqualTo(Integer value) {
            addCriterion("element_id <=", value, "elementId");
            return (Criteria)this;
        }

        public Criteria andElementIdIn(List<Integer> values) {
            addCriterion("element_id in", values, "elementId");
            return (Criteria)this;
        }

        public Criteria andElementIdNotIn(List<Integer> values) {
            addCriterion("element_id not in", values, "elementId");
            return (Criteria)this;
        }

        public Criteria andElementIdBetween(Integer value1, Integer value2) {
            addCriterion("element_id between", value1, value2, "elementId");
            return (Criteria)this;
        }

        public Criteria andElementIdNotBetween(Integer value1, Integer value2) {
            addCriterion("element_id not between", value1, value2, "elementId");
            return (Criteria)this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria)this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria)this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria)this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria)this;
        }

        public Criteria andOperatorIdEqualTo(Integer value) {
            addCriterion("operator_id =", value, "operatorId");
            return (Criteria)this;
        }

        public Criteria andElementKeyEqualTo(String value) {
            addCriterion("element_key =", value, "elementKey");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria)this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria)this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andElementIncludesTypes(List<String> values) {
            if (values == null || values.isEmpty()) {
                return (Criteria)this;
            }

            StringBuilder criterionBuilder = new StringBuilder();

            // 构建查询条件：查找level=2的元素和其子元素
            criterionBuilder.append("element_id in (");

            // 1. 获取所有level=2且type匹配的ID
            criterionBuilder.append("select id from creative_element where deleted = 0 and level = 2 and (");

            for (int i = 0; i < values.size(); i++) {
                if (i > 0) {
                    criterionBuilder.append(" AND ");
                }
                criterionBuilder.append("FIND_IN_SET('").append(values.get(i)).append("', type) > 0");
            }

            criterionBuilder.append(")");

            // 2. 获取所有以上面ID为parentId的level=3的ID
            criterionBuilder.append(" UNION ");
            criterionBuilder.append(
                "select id from creative_element where deleted = 0 and level = 3 and parent_id in (");
            criterionBuilder.append("select id from creative_element where deleted = 0 and level = 2 and (");

            for (int i = 0; i < values.size(); i++) {
                if (i > 0) {
                    criterionBuilder.append(" AND ");
                }
                criterionBuilder.append("FIND_IN_SET('").append(values.get(i)).append("', type) > 0");
            }

            criterionBuilder.append("))");

            criterionBuilder.append(")");

            addCriterion(criterionBuilder.toString());
            return (Criteria)this;
        }

        public Criteria andElementExcludesTypes(List<String> values) {
            if (values == null || values.isEmpty()) {
                return (Criteria)this;
            }

            StringBuilder criterionBuilder = new StringBuilder();

            // 首先查找level=2且匹配type条件的元素
            criterionBuilder.append("element_id not in (");

            // 1. 获取所有level=2且type匹配的ID
            criterionBuilder.append("select id from creative_element where deleted = 0 and level = 2 and (");

            for (int i = 0; i < values.size(); i++) {
                if (i > 0) {
                    criterionBuilder.append(" OR ");
                }
                criterionBuilder.append("FIND_IN_SET('").append(values.get(i)).append("', type) > 0");
            }

            criterionBuilder.append(")");

            // 2. 获取所有以上面ID为parentId的level=3的ID
            criterionBuilder.append(" UNION ");
            criterionBuilder.append(
                "select id from creative_element where deleted = 0 and level = 3 and parent_id in (");
            criterionBuilder.append("select id from creative_element where deleted = 0 and level = 2 and (");

            for (int i = 0; i < values.size(); i++) {
                if (i > 0) {
                    criterionBuilder.append(" OR ");
                }
                criterionBuilder.append("FIND_IN_SET('").append(values.get(i)).append("', type) > 0");
            }

            criterionBuilder.append("))");

            criterionBuilder.append(")");

            addCriterion(criterionBuilder.toString());
            return (Criteria)this;
        }

        public Criteria andElementExtEquals(String key, Object value) {

            String criterion = String.format(

                "element_id in ("

                + "(select id from creative_element where deleted = 0 and level = 2 and "
                + " (JSON_UNQUOTE(JSON_EXTRACT" + "(ext_info, '$.%s')) is not null and JSON_UNQUOTE(JSON_EXTRACT"
                + "(ext_info, '$.%s')) = '%s'))"

                + " UNION "

                + "(select id from creative_element where deleted = 0 and level = 3 and parent_id in ("
                + "select id from creative_element where deleted = 0 and level = 2 and (JSON_UNQUOTE(JSON_EXTRACT"
                + "(ext_info, '$.%s')) is not null and JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.%s')) = '%s')))"

                + ")", key, key, value, key, key, value);

            addCriterion(criterion);
            return (Criteria)this;
        }

        public Criteria andElementExtNotEquals(String key, Object value) {

            String criterion = String.format(

                "element_id in ("

                + "(select id from creative_element where deleted = 0 and level = 2 and "
                + " (JSON_UNQUOTE(JSON_EXTRACT" + "(ext_info, '$.%s')) is null or JSON_UNQUOTE(JSON_EXTRACT"
                + "(ext_info, '$.%s')) != '%s'))"

                + " UNION "

                + "(select id from creative_element where deleted = 0 and level = 3 and parent_id in ("
                + "select id from creative_element where deleted = 0 and level = 2 and (JSON_UNQUOTE(JSON_EXTRACT"
                + "(ext_info, '$.%s')) is null or JSON_UNQUOTE(JSON_EXTRACT(ext_info, '$.%s')) != '%s')))"

                + ")", key, key, value, key, key, value);


            addCriterion(criterion);
            return (Criteria)this;
        }

    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}