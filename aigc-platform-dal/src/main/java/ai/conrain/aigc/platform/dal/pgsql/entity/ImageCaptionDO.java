package ai.conrain.aigc.platform.dal.pgsql.entity;

import com.pgvector.PGvector;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 图像标注表，存储图像的多模态特征向量及标注文本，用于内容检索和分类
 * 对应数据表：image_caption
 */
@Data
public class ImageCaptionDO implements Serializable {
    /**
     * 自增主键ID
     */
    private Integer id;

    /**
     * 关联的图像ID，用于跨表查询
     */
    private Integer imageId;

    /**
     * 图像标注的完整文本描述，JSON格式存储
     */
    private String caption;

    /**
     * 标注版本号，用于区分不同标注模型或规则
     */
    private String captionVersion;

    /**
     * imageType
     */
    private String imageType;

    /**
     * clothGenderType
     */
    private String clothGenderType;

    /**
     * 流派
     */
    private String genre;

    /**
     * ageGroup，年龄段
     */
    private String ageGroup;

    /**
     * 图像质量评分,0-1之间
     */
    private Double qualityScore;

    /**
     * 整图特征向量（1024维）
     */
    private PGvector imgEmb;

    /**
     * 背景抠图特征向量（1024维）
     */
    private PGvector bgImgEmb;

    /**
     * 模特面部抠图特征向量（1024维）
     */
    private PGvector modelFacialImgEmb;

    /**
     * 模特姿势抠图特征向量（1024维）
     */
    private PGvector modelPoseImgEmb;

    /**
     * 服装款式描述文本向量
     */
    private PGvector clothStyleTextEmb;

    /**
     * 服装整体描述文本向量
     */
    private PGvector clothTextEmb;

    /**
     * 背景道具描述文本向量
     */
    private PGvector bgTextEmb;

    /**
     * 配饰描述文本向量
     */
    private PGvector accessoriesTextEmb;

    /**
     * 发型描述文本向量
     */
    private PGvector hairstyleTextEmb;

    /**
     * 姿势描述文本向量
     */
    private PGvector poseTextEmb;

    /**
     * 背景分类文本向量
     */
    private PGvector sortBgTextEmb;

    /**
     * 表情分类文本向量
     */
    private PGvector sortFacialExpressionTextEmb;

    /**
     * 配饰分类文本向量
     */
    private PGvector sortAccessoriesTextEmb;

    /**
     * 姿势分类文本向量
     */
    private PGvector sortPoseTextEmb;

    /**
     * 扩展信息，JSON格式存储非结构化附加数据
     */
    private String extInfo;

    /**
     * 记录创建时间，默认为当前时间戳
     */
    private Date createTime;

    /**
     * 记录最后修改时间，默认为当前时间戳
     */
    private Date modifyTime;

    /**
     * 软删除标记，FALSE表示未删除，TRUE表示已删除
     */
    private Boolean deleted;

    //----------------------view columns----------------------
    //余弦相似度，取值范围是[-1,1]，其中，1表示完全相似，-1表示完全相反，0表示毫不相关；余弦相似度=pgvector的1-cos(x,y)，其中cos(x,y)指定的是<=>得到的余弦距离（取值范围[0,2]），注意需要和<=>配合使用
    private Double clothStyleSimilarity;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}