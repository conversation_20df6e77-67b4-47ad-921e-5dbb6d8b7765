package ai.conrain.aigc.platform.dal.example;

import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import com.pgvector.PGvector;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ImageCaptionExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer rows;

    private Integer offset;

    public ImageCaptionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return rows;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andImageIdIsNull() {
            addCriterion("image_id is null");
            return (Criteria) this;
        }

        public Criteria andImageIdIsNotNull() {
            addCriterion("image_id is not null");
            return (Criteria) this;
        }

        public Criteria andImageIdEqualTo(Integer value) {
            addCriterion("image_id =", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdNotEqualTo(Integer value) {
            addCriterion("image_id <>", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdGreaterThan(Integer value) {
            addCriterion("image_id >", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("image_id >=", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdLessThan(Integer value) {
            addCriterion("image_id <", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdLessThanOrEqualTo(Integer value) {
            addCriterion("image_id <=", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdIn(List<Integer> values) {
            addCriterion("image_id in", values, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdNotIn(List<Integer> values) {
            addCriterion("image_id not in", values, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdBetween(Integer value1, Integer value2) {
            addCriterion("image_id between", value1, value2, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("image_id not between", value1, value2, "imageId");
            return (Criteria) this;
        }

        public Criteria andCaptionIsNull() {
            addCriterion("caption is null");
            return (Criteria) this;
        }

        public Criteria andCaptionIsNotNull() {
            addCriterion("caption is not null");
            return (Criteria) this;
        }

        public Criteria andCaptionEqualTo(String value) {
            addCriterion("caption =", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotEqualTo(String value) {
            addCriterion("caption <>", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionGreaterThan(String value) {
            addCriterion("caption >", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionGreaterThanOrEqualTo(String value) {
            addCriterion("caption >=", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionLessThan(String value) {
            addCriterion("caption <", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionLessThanOrEqualTo(String value) {
            addCriterion("caption <=", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionIn(List<String> values) {
            addCriterion("caption in", values, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotIn(List<String> values) {
            addCriterion("caption not in", values, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionBetween(String value1, String value2) {
            addCriterion("caption between", value1, value2, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotBetween(String value1, String value2) {
            addCriterion("caption not between", value1, value2, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionIsNull() {
            addCriterion("caption_version is null");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionIsNotNull() {
            addCriterion("caption_version is not null");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionEqualTo(String value) {
            addCriterion("caption_version =", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionNotEqualTo(String value) {
            addCriterion("caption_version <>", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionGreaterThan(String value) {
            addCriterion("caption_version >", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionGreaterThanOrEqualTo(String value) {
            addCriterion("caption_version >=", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionLessThan(String value) {
            addCriterion("caption_version <", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionLessThanOrEqualTo(String value) {
            addCriterion("caption_version <=", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionLike(String value) {
            addCriterion("caption_version like", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionNotLike(String value) {
            addCriterion("caption_version not like", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionIn(List<String> values) {
            addCriterion("caption_version in", values, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionNotIn(List<String> values) {
            addCriterion("caption_version not in", values, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionBetween(String value1, String value2) {
            addCriterion("caption_version between", value1, value2, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionNotBetween(String value1, String value2) {
            addCriterion("caption_version not between", value1, value2, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andImgEmbIsNull() {
            addCriterion("img_emb is null");
            return (Criteria) this;
        }

        public Criteria andImgEmbIsNotNull() {
            addCriterion("img_emb is not null");
            return (Criteria) this;
        }

        public Criteria andImgEmbEqualTo(PGvector value) {
            addCriterion("img_emb =", value, "imgEmb");
            return (Criteria) this;
        }

        public Criteria andImgEmbNotEqualTo(PGvector value) {
            addCriterion("img_emb <>", value, "imgEmb");
            return (Criteria) this;
        }

        public Criteria andImgEmbGreaterThan(PGvector value) {
            addCriterion("img_emb >", value, "imgEmb");
            return (Criteria) this;
        }

        public Criteria andImgEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("img_emb >=", value, "imgEmb");
            return (Criteria) this;
        }

        public Criteria andImgEmbLessThan(PGvector value) {
            addCriterion("img_emb <", value, "imgEmb");
            return (Criteria) this;
        }

        public Criteria andImgEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("img_emb <=", value, "imgEmb");
            return (Criteria) this;
        }

        public Criteria andImgEmbIn(List<PGvector> values) {
            addCriterion("img_emb in", values, "imgEmb");
            return (Criteria) this;
        }

        public Criteria andImgEmbNotIn(List<PGvector> values) {
            addCriterion("img_emb not in", values, "imgEmb");
            return (Criteria) this;
        }

        public Criteria andImgEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("img_emb between", value1, value2, "imgEmb");
            return (Criteria) this;
        }

        public Criteria andImgEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("img_emb not between", value1, value2, "imgEmb");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbIsNull() {
            addCriterion("bg_img_emb is null");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbIsNotNull() {
            addCriterion("bg_img_emb is not null");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbEqualTo(PGvector value) {
            addCriterion("bg_img_emb =", value, "bgImgEmb");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbNotEqualTo(PGvector value) {
            addCriterion("bg_img_emb <>", value, "bgImgEmb");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbGreaterThan(PGvector value) {
            addCriterion("bg_img_emb >", value, "bgImgEmb");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("bg_img_emb >=", value, "bgImgEmb");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbLessThan(PGvector value) {
            addCriterion("bg_img_emb <", value, "bgImgEmb");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("bg_img_emb <=", value, "bgImgEmb");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbIn(List<PGvector> values) {
            addCriterion("bg_img_emb in", values, "bgImgEmb");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbNotIn(List<PGvector> values) {
            addCriterion("bg_img_emb not in", values, "bgImgEmb");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("bg_img_emb between", value1, value2, "bgImgEmb");
            return (Criteria) this;
        }

        public Criteria andBgImgEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("bg_img_emb not between", value1, value2, "bgImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbIsNull() {
            addCriterion("model_facial_img_emb is null");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbIsNotNull() {
            addCriterion("model_facial_img_emb is not null");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbEqualTo(PGvector value) {
            addCriterion("model_facial_img_emb =", value, "modelFacialImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbNotEqualTo(PGvector value) {
            addCriterion("model_facial_img_emb <>", value, "modelFacialImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbGreaterThan(PGvector value) {
            addCriterion("model_facial_img_emb >", value, "modelFacialImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("model_facial_img_emb >=", value, "modelFacialImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbLessThan(PGvector value) {
            addCriterion("model_facial_img_emb <", value, "modelFacialImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("model_facial_img_emb <=", value, "modelFacialImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbIn(List<PGvector> values) {
            addCriterion("model_facial_img_emb in", values, "modelFacialImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbNotIn(List<PGvector> values) {
            addCriterion("model_facial_img_emb not in", values, "modelFacialImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("model_facial_img_emb between", value1, value2, "modelFacialImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelFacialImgEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("model_facial_img_emb not between", value1, value2, "modelFacialImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbIsNull() {
            addCriterion("model_pose_img_emb is null");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbIsNotNull() {
            addCriterion("model_pose_img_emb is not null");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbEqualTo(PGvector value) {
            addCriterion("model_pose_img_emb =", value, "modelPoseImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbNotEqualTo(PGvector value) {
            addCriterion("model_pose_img_emb <>", value, "modelPoseImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbGreaterThan(PGvector value) {
            addCriterion("model_pose_img_emb >", value, "modelPoseImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("model_pose_img_emb >=", value, "modelPoseImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbLessThan(PGvector value) {
            addCriterion("model_pose_img_emb <", value, "modelPoseImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("model_pose_img_emb <=", value, "modelPoseImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbIn(List<PGvector> values) {
            addCriterion("model_pose_img_emb in", values, "modelPoseImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbNotIn(List<PGvector> values) {
            addCriterion("model_pose_img_emb not in", values, "modelPoseImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("model_pose_img_emb between", value1, value2, "modelPoseImgEmb");
            return (Criteria) this;
        }

        public Criteria andModelPoseImgEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("model_pose_img_emb not between", value1, value2, "modelPoseImgEmb");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbIsNull() {
            addCriterion("cloth_style_text_emb is null");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbIsNotNull() {
            addCriterion("cloth_style_text_emb is not null");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbEqualTo(PGvector value) {
            addCriterion("cloth_style_text_emb =", value, "clothStyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbNotEqualTo(PGvector value) {
            addCriterion("cloth_style_text_emb <>", value, "clothStyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbGreaterThan(PGvector value) {
            addCriterion("cloth_style_text_emb >", value, "clothStyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("cloth_style_text_emb >=", value, "clothStyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbLessThan(PGvector value) {
            addCriterion("cloth_style_text_emb <", value, "clothStyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("cloth_style_text_emb <=", value, "clothStyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbIn(List<PGvector> values) {
            addCriterion("cloth_style_text_emb in", values, "clothStyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbNotIn(List<PGvector> values) {
            addCriterion("cloth_style_text_emb not in", values, "clothStyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("cloth_style_text_emb between", value1, value2, "clothStyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothStyleTextEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("cloth_style_text_emb not between", value1, value2, "clothStyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbIsNull() {
            addCriterion("cloth_text_emb is null");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbIsNotNull() {
            addCriterion("cloth_text_emb is not null");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbEqualTo(PGvector value) {
            addCriterion("cloth_text_emb =", value, "clothTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbNotEqualTo(PGvector value) {
            addCriterion("cloth_text_emb <>", value, "clothTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbGreaterThan(PGvector value) {
            addCriterion("cloth_text_emb >", value, "clothTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("cloth_text_emb >=", value, "clothTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbLessThan(PGvector value) {
            addCriterion("cloth_text_emb <", value, "clothTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("cloth_text_emb <=", value, "clothTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbIn(List<PGvector> values) {
            addCriterion("cloth_text_emb in", values, "clothTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbNotIn(List<PGvector> values) {
            addCriterion("cloth_text_emb not in", values, "clothTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("cloth_text_emb between", value1, value2, "clothTextEmb");
            return (Criteria) this;
        }

        public Criteria andClothTextEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("cloth_text_emb not between", value1, value2, "clothTextEmb");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbIsNull() {
            addCriterion("bg_text_emb is null");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbIsNotNull() {
            addCriterion("bg_text_emb is not null");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbEqualTo(PGvector value) {
            addCriterion("bg_text_emb =", value, "bgTextEmb");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbNotEqualTo(PGvector value) {
            addCriterion("bg_text_emb <>", value, "bgTextEmb");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbGreaterThan(PGvector value) {
            addCriterion("bg_text_emb >", value, "bgTextEmb");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("bg_text_emb >=", value, "bgTextEmb");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbLessThan(PGvector value) {
            addCriterion("bg_text_emb <", value, "bgTextEmb");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("bg_text_emb <=", value, "bgTextEmb");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbIn(List<PGvector> values) {
            addCriterion("bg_text_emb in", values, "bgTextEmb");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbNotIn(List<PGvector> values) {
            addCriterion("bg_text_emb not in", values, "bgTextEmb");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("bg_text_emb between", value1, value2, "bgTextEmb");
            return (Criteria) this;
        }

        public Criteria andBgTextEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("bg_text_emb not between", value1, value2, "bgTextEmb");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbIsNull() {
            addCriterion("accessories_text_emb is null");
            return (Criteria) this;
        }

        public Criteria andQualityScoreIsNull() {
            addCriterion("quality_score is null");
            return (Criteria) this;
        }

        public Criteria andImgEmbIsZero() {
            addCriterion("img_emb::text like '%[0,0%'");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbIsNotNull() {
            addCriterion("accessories_text_emb is not null");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbEqualTo(PGvector value) {
            addCriterion("accessories_text_emb =", value, "accessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbNotEqualTo(PGvector value) {
            addCriterion("accessories_text_emb <>", value, "accessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbGreaterThan(PGvector value) {
            addCriterion("accessories_text_emb >", value, "accessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("accessories_text_emb >=", value, "accessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbLessThan(PGvector value) {
            addCriterion("accessories_text_emb <", value, "accessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("accessories_text_emb <=", value, "accessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbIn(List<PGvector> values) {
            addCriterion("accessories_text_emb in", values, "accessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbNotIn(List<PGvector> values) {
            addCriterion("accessories_text_emb not in", values, "accessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("accessories_text_emb between", value1, value2, "accessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andAccessoriesTextEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("accessories_text_emb not between", value1, value2, "accessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbIsNull() {
            addCriterion("hairstyle_text_emb is null");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbIsNotNull() {
            addCriterion("hairstyle_text_emb is not null");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbEqualTo(PGvector value) {
            addCriterion("hairstyle_text_emb =", value, "hairstyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbNotEqualTo(PGvector value) {
            addCriterion("hairstyle_text_emb <>", value, "hairstyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbGreaterThan(PGvector value) {
            addCriterion("hairstyle_text_emb >", value, "hairstyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("hairstyle_text_emb >=", value, "hairstyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbLessThan(PGvector value) {
            addCriterion("hairstyle_text_emb <", value, "hairstyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("hairstyle_text_emb <=", value, "hairstyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbIn(List<PGvector> values) {
            addCriterion("hairstyle_text_emb in", values, "hairstyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbNotIn(List<PGvector> values) {
            addCriterion("hairstyle_text_emb not in", values, "hairstyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("hairstyle_text_emb between", value1, value2, "hairstyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andHairstyleTextEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("hairstyle_text_emb not between", value1, value2, "hairstyleTextEmb");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbIsNull() {
            addCriterion("pose_text_emb is null");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbIsNotNull() {
            addCriterion("pose_text_emb is not null");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbEqualTo(PGvector value) {
            addCriterion("pose_text_emb =", value, "poseTextEmb");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbNotEqualTo(PGvector value) {
            addCriterion("pose_text_emb <>", value, "poseTextEmb");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbGreaterThan(PGvector value) {
            addCriterion("pose_text_emb >", value, "poseTextEmb");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("pose_text_emb >=", value, "poseTextEmb");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbLessThan(PGvector value) {
            addCriterion("pose_text_emb <", value, "poseTextEmb");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("pose_text_emb <=", value, "poseTextEmb");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbIn(List<PGvector> values) {
            addCriterion("pose_text_emb in", values, "poseTextEmb");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbNotIn(List<PGvector> values) {
            addCriterion("pose_text_emb not in", values, "poseTextEmb");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("pose_text_emb between", value1, value2, "poseTextEmb");
            return (Criteria) this;
        }

        public Criteria andPoseTextEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("pose_text_emb not between", value1, value2, "poseTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbIsNull() {
            addCriterion("sort_bg_text_emb is null");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbIsNotNull() {
            addCriterion("sort_bg_text_emb is not null");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbEqualTo(PGvector value) {
            addCriterion("sort_bg_text_emb =", value, "sortBgTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbNotEqualTo(PGvector value) {
            addCriterion("sort_bg_text_emb <>", value, "sortBgTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbGreaterThan(PGvector value) {
            addCriterion("sort_bg_text_emb >", value, "sortBgTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("sort_bg_text_emb >=", value, "sortBgTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbLessThan(PGvector value) {
            addCriterion("sort_bg_text_emb <", value, "sortBgTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("sort_bg_text_emb <=", value, "sortBgTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbIn(List<PGvector> values) {
            addCriterion("sort_bg_text_emb in", values, "sortBgTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbNotIn(List<PGvector> values) {
            addCriterion("sort_bg_text_emb not in", values, "sortBgTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("sort_bg_text_emb between", value1, value2, "sortBgTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortBgTextEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("sort_bg_text_emb not between", value1, value2, "sortBgTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbIsNull() {
            addCriterion("sort_facial_expression_text_emb is null");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbIsNotNull() {
            addCriterion("sort_facial_expression_text_emb is not null");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbEqualTo(PGvector value) {
            addCriterion("sort_facial_expression_text_emb =", value, "sortFacialExpressionTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbNotEqualTo(PGvector value) {
            addCriterion("sort_facial_expression_text_emb <>", value, "sortFacialExpressionTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbGreaterThan(PGvector value) {
            addCriterion("sort_facial_expression_text_emb >", value, "sortFacialExpressionTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("sort_facial_expression_text_emb >=", value, "sortFacialExpressionTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbLessThan(PGvector value) {
            addCriterion("sort_facial_expression_text_emb <", value, "sortFacialExpressionTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("sort_facial_expression_text_emb <=", value, "sortFacialExpressionTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbIn(List<PGvector> values) {
            addCriterion("sort_facial_expression_text_emb in", values, "sortFacialExpressionTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbNotIn(List<PGvector> values) {
            addCriterion("sort_facial_expression_text_emb not in", values, "sortFacialExpressionTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("sort_facial_expression_text_emb between", value1, value2, "sortFacialExpressionTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortFacialExpressionTextEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("sort_facial_expression_text_emb not between", value1, value2, "sortFacialExpressionTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbIsNull() {
            addCriterion("sort_accessories_text_emb is null");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbIsNotNull() {
            addCriterion("sort_accessories_text_emb is not null");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbEqualTo(PGvector value) {
            addCriterion("sort_accessories_text_emb =", value, "sortAccessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbNotEqualTo(PGvector value) {
            addCriterion("sort_accessories_text_emb <>", value, "sortAccessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbGreaterThan(PGvector value) {
            addCriterion("sort_accessories_text_emb >", value, "sortAccessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("sort_accessories_text_emb >=", value, "sortAccessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbLessThan(PGvector value) {
            addCriterion("sort_accessories_text_emb <", value, "sortAccessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("sort_accessories_text_emb <=", value, "sortAccessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbIn(List<PGvector> values) {
            addCriterion("sort_accessories_text_emb in", values, "sortAccessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbNotIn(List<PGvector> values) {
            addCriterion("sort_accessories_text_emb not in", values, "sortAccessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("sort_accessories_text_emb between", value1, value2, "sortAccessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortAccessoriesTextEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("sort_accessories_text_emb not between", value1, value2, "sortAccessoriesTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbIsNull() {
            addCriterion("sort_pose_text_emb is null");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbIsNotNull() {
            addCriterion("sort_pose_text_emb is not null");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbEqualTo(PGvector value) {
            addCriterion("sort_pose_text_emb =", value, "sortPoseTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbNotEqualTo(PGvector value) {
            addCriterion("sort_pose_text_emb <>", value, "sortPoseTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbGreaterThan(PGvector value) {
            addCriterion("sort_pose_text_emb >", value, "sortPoseTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbGreaterThanOrEqualTo(PGvector value) {
            addCriterion("sort_pose_text_emb >=", value, "sortPoseTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbLessThan(PGvector value) {
            addCriterion("sort_pose_text_emb <", value, "sortPoseTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbLessThanOrEqualTo(PGvector value) {
            addCriterion("sort_pose_text_emb <=", value, "sortPoseTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbIn(List<PGvector> values) {
            addCriterion("sort_pose_text_emb in", values, "sortPoseTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbNotIn(List<PGvector> values) {
            addCriterion("sort_pose_text_emb not in", values, "sortPoseTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbBetween(PGvector value1, PGvector value2) {
            addCriterion("sort_pose_text_emb between", value1, value2, "sortPoseTextEmb");
            return (Criteria) this;
        }

        public Criteria andSortPoseTextEmbNotBetween(PGvector value1, PGvector value2) {
            addCriterion("sort_pose_text_emb not between", value1, value2, "sortPoseTextEmb");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andImageTypeIsNull() {
            addCriterion("image_type is null");
            return (Criteria) this;
        }

        public Criteria andImageTypeIsNotNull() {
            addCriterion("image_type is not null");
            return (Criteria) this;
        }

        public Criteria andImageTypeEqualTo(String value) {
            addCriterion("image_type =", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeNotEqualTo(String value) {
            addCriterion("image_type <>", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeGreaterThan(String value) {
            addCriterion("image_type >", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeGreaterThanOrEqualTo(String value) {
            addCriterion("image_type >=", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeLessThan(String value) {
            addCriterion("image_type <", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeLessThanOrEqualTo(String value) {
            addCriterion("image_type <=", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeLike(String value) {
            addCriterion("image_type like", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeNotLike(String value) {
            addCriterion("image_type not like", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeIn(List<String> values) {
            addCriterion("image_type in", values, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeNotIn(List<String> values) {
            addCriterion("image_type not in", values, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeBetween(String value1, String value2) {
            addCriterion("image_type between", value1, value2, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeNotBetween(String value1, String value2) {
            addCriterion("image_type not between", value1, value2, "imageType");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeIsNull() {
            addCriterion("cloth_gender_type is null");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeIsNotNull() {
            addCriterion("cloth_gender_type is not null");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeEqualTo(String value) {
            addCriterion("cloth_gender_type =", value, "clothGenderType");
            return (Criteria) this;
        }

        public Criteria andAgeGroupEqualTo(String value) {
            addCriterion("age_group =", value, "ageGroup");
            return (Criteria) this;
        }
        public Criteria andGenreEqualTo(String value) {
            addCriterion("genre =", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreIsNull() {
            addCriterion("genre is null");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeNotEqualTo(String value) {
            addCriterion("cloth_gender_type <>", value, "clothGenderType");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeGreaterThan(String value) {
            addCriterion("cloth_gender_type >", value, "clothGenderType");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeGreaterThanOrEqualTo(String value) {
            addCriterion("cloth_gender_type >=", value, "clothGenderType");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeLessThan(String value) {
            addCriterion("cloth_gender_type <", value, "clothGenderType");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeLessThanOrEqualTo(String value) {
            addCriterion("cloth_gender_type <=", value, "clothGenderType");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeLike(String value) {
            addCriterion("cloth_gender_type like", value, "clothGenderType");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeNotLike(String value) {
            addCriterion("cloth_gender_type not like", value, "clothGenderType");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeIn(List<String> values) {
            addCriterion("cloth_gender_type in", values, "clothGenderType");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeNotIn(List<String> values) {
            addCriterion("cloth_gender_type not in", values, "clothGenderType");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeBetween(String value1, String value2) {
            addCriterion("cloth_gender_type between", value1, value2, "clothGenderType");
            return (Criteria) this;
        }

        public Criteria andClothGenderTypeNotBetween(String value1, String value2) {
            addCriterion("cloth_gender_type not between", value1, value2, "clothGenderType");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }

        public Criteria andLogicalDeleted(boolean deleted) {
            return deleted ? andDeletedEqualTo(ImageCaptionDO.Deleted.IS_DELETED.value()) : andDeletedNotEqualTo(ImageCaptionDO.Deleted.IS_DELETED.value());
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}