package ai.conrain.aigc.platform.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SearchTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer rows;

    private Integer offset;

    public SearchTaskExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return rows;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdIsNull() {
            addCriterion("user_session_id is null");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdIsNotNull() {
            addCriterion("user_session_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdEqualTo(Integer value) {
            addCriterion("user_session_id =", value, "userSessionId");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdNotEqualTo(Integer value) {
            addCriterion("user_session_id <>", value, "userSessionId");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdGreaterThan(Integer value) {
            addCriterion("user_session_id >", value, "userSessionId");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_session_id >=", value, "userSessionId");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdLessThan(Integer value) {
            addCriterion("user_session_id <", value, "userSessionId");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_session_id <=", value, "userSessionId");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdIn(List<Integer> values) {
            addCriterion("user_session_id in", values, "userSessionId");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdNotIn(List<Integer> values) {
            addCriterion("user_session_id not in", values, "userSessionId");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdBetween(Integer value1, Integer value2) {
            addCriterion("user_session_id between", value1, value2, "userSessionId");
            return (Criteria) this;
        }

        public Criteria andUserSessionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_session_id not between", value1, value2, "userSessionId");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdIsNull() {
            addCriterion("user_session_task_id is null");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdIsNotNull() {
            addCriterion("user_session_task_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdEqualTo(Integer value) {
            addCriterion("user_session_task_id =", value, "userSessionTaskId");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdNotEqualTo(Integer value) {
            addCriterion("user_session_task_id <>", value, "userSessionTaskId");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdGreaterThan(Integer value) {
            addCriterion("user_session_task_id >", value, "userSessionTaskId");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_session_task_id >=", value, "userSessionTaskId");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdLessThan(Integer value) {
            addCriterion("user_session_task_id <", value, "userSessionTaskId");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_session_task_id <=", value, "userSessionTaskId");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdIn(List<Integer> values) {
            addCriterion("user_session_task_id in", values, "userSessionTaskId");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdNotIn(List<Integer> values) {
            addCriterion("user_session_task_id not in", values, "userSessionTaskId");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdBetween(Integer value1, Integer value2) {
            addCriterion("user_session_task_id between", value1, value2, "userSessionTaskId");
            return (Criteria) this;
        }

        public Criteria andUserSessionTaskIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_session_task_id not between", value1, value2, "userSessionTaskId");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailIsNull() {
            addCriterion("cloth_img_detail is null");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailIsNotNull() {
            addCriterion("cloth_img_detail is not null");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailEqualTo(String value) {
            addCriterion("cloth_img_detail =", value, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailNotEqualTo(String value) {
            addCriterion("cloth_img_detail <>", value, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailGreaterThan(String value) {
            addCriterion("cloth_img_detail >", value, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailGreaterThanOrEqualTo(String value) {
            addCriterion("cloth_img_detail >=", value, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailLessThan(String value) {
            addCriterion("cloth_img_detail <", value, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailLessThanOrEqualTo(String value) {
            addCriterion("cloth_img_detail <=", value, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailLike(String value) {
            addCriterion("cloth_img_detail like", value, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailNotLike(String value) {
            addCriterion("cloth_img_detail not like", value, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailIn(List<String> values) {
            addCriterion("cloth_img_detail in", values, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailNotIn(List<String> values) {
            addCriterion("cloth_img_detail not in", values, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailBetween(String value1, String value2) {
            addCriterion("cloth_img_detail between", value1, value2, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothImgDetailNotBetween(String value1, String value2) {
            addCriterion("cloth_img_detail not between", value1, value2, "clothImgDetail");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisIsNull() {
            addCriterion("cloth_analysis is null");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisIsNotNull() {
            addCriterion("cloth_analysis is not null");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisEqualTo(String value) {
            addCriterion("cloth_analysis =", value, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisNotEqualTo(String value) {
            addCriterion("cloth_analysis <>", value, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisGreaterThan(String value) {
            addCriterion("cloth_analysis >", value, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisGreaterThanOrEqualTo(String value) {
            addCriterion("cloth_analysis >=", value, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisLessThan(String value) {
            addCriterion("cloth_analysis <", value, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisLessThanOrEqualTo(String value) {
            addCriterion("cloth_analysis <=", value, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisLike(String value) {
            addCriterion("cloth_analysis like", value, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisNotLike(String value) {
            addCriterion("cloth_analysis not like", value, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisIn(List<String> values) {
            addCriterion("cloth_analysis in", values, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisNotIn(List<String> values) {
            addCriterion("cloth_analysis not in", values, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisBetween(String value1, String value2) {
            addCriterion("cloth_analysis between", value1, value2, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andClothAnalysisNotBetween(String value1, String value2) {
            addCriterion("cloth_analysis not between", value1, value2, "clothAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailIsNull() {
            addCriterion("ref_img_detail is null");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailIsNotNull() {
            addCriterion("ref_img_detail is not null");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailEqualTo(String value) {
            addCriterion("ref_img_detail =", value, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailNotEqualTo(String value) {
            addCriterion("ref_img_detail <>", value, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailGreaterThan(String value) {
            addCriterion("ref_img_detail >", value, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailGreaterThanOrEqualTo(String value) {
            addCriterion("ref_img_detail >=", value, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailLessThan(String value) {
            addCriterion("ref_img_detail <", value, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailLessThanOrEqualTo(String value) {
            addCriterion("ref_img_detail <=", value, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailLike(String value) {
            addCriterion("ref_img_detail like", value, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailNotLike(String value) {
            addCriterion("ref_img_detail not like", value, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailIn(List<String> values) {
            addCriterion("ref_img_detail in", values, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailNotIn(List<String> values) {
            addCriterion("ref_img_detail not in", values, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailBetween(String value1, String value2) {
            addCriterion("ref_img_detail between", value1, value2, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgDetailNotBetween(String value1, String value2) {
            addCriterion("ref_img_detail not between", value1, value2, "refImgDetail");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisIsNull() {
            addCriterion("ref_img_analysis is null");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisIsNotNull() {
            addCriterion("ref_img_analysis is not null");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisEqualTo(String value) {
            addCriterion("ref_img_analysis =", value, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisNotEqualTo(String value) {
            addCriterion("ref_img_analysis <>", value, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisGreaterThan(String value) {
            addCriterion("ref_img_analysis >", value, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisGreaterThanOrEqualTo(String value) {
            addCriterion("ref_img_analysis >=", value, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisLessThan(String value) {
            addCriterion("ref_img_analysis <", value, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisLessThanOrEqualTo(String value) {
            addCriterion("ref_img_analysis <=", value, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisLike(String value) {
            addCriterion("ref_img_analysis like", value, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisNotLike(String value) {
            addCriterion("ref_img_analysis not like", value, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisIn(List<String> values) {
            addCriterion("ref_img_analysis in", values, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisNotIn(List<String> values) {
            addCriterion("ref_img_analysis not in", values, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisBetween(String value1, String value2) {
            addCriterion("ref_img_analysis between", value1, value2, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andRefImgAnalysisNotBetween(String value1, String value2) {
            addCriterion("ref_img_analysis not between", value1, value2, "refImgAnalysis");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsIsNull() {
            addCriterion("search_options is null");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsIsNotNull() {
            addCriterion("search_options is not null");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsEqualTo(String value) {
            addCriterion("search_options =", value, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsNotEqualTo(String value) {
            addCriterion("search_options <>", value, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsGreaterThan(String value) {
            addCriterion("search_options >", value, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsGreaterThanOrEqualTo(String value) {
            addCriterion("search_options >=", value, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsLessThan(String value) {
            addCriterion("search_options <", value, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsLessThanOrEqualTo(String value) {
            addCriterion("search_options <=", value, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsLike(String value) {
            addCriterion("search_options like", value, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsNotLike(String value) {
            addCriterion("search_options not like", value, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsIn(List<String> values) {
            addCriterion("search_options in", values, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsNotIn(List<String> values) {
            addCriterion("search_options not in", values, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsBetween(String value1, String value2) {
            addCriterion("search_options between", value1, value2, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andSearchOptionsNotBetween(String value1, String value2) {
            addCriterion("search_options not between", value1, value2, "searchOptions");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andRetSummaryIsNull() {
            addCriterion("ret_summary is null");
            return (Criteria) this;
        }

        public Criteria andRetSummaryIsNotNull() {
            addCriterion("ret_summary is not null");
            return (Criteria) this;
        }

        public Criteria andRetSummaryEqualTo(String value) {
            addCriterion("ret_summary =", value, "retSummary");
            return (Criteria) this;
        }

        public Criteria andRetSummaryNotEqualTo(String value) {
            addCriterion("ret_summary <>", value, "retSummary");
            return (Criteria) this;
        }

        public Criteria andRetSummaryGreaterThan(String value) {
            addCriterion("ret_summary >", value, "retSummary");
            return (Criteria) this;
        }

        public Criteria andRetSummaryGreaterThanOrEqualTo(String value) {
            addCriterion("ret_summary >=", value, "retSummary");
            return (Criteria) this;
        }

        public Criteria andRetSummaryLessThan(String value) {
            addCriterion("ret_summary <", value, "retSummary");
            return (Criteria) this;
        }

        public Criteria andRetSummaryLessThanOrEqualTo(String value) {
            addCriterion("ret_summary <=", value, "retSummary");
            return (Criteria) this;
        }

        public Criteria andRetSummaryLike(String value) {
            addCriterion("ret_summary like", value, "retSummary");
            return (Criteria) this;
        }

        public Criteria andRetSummaryNotLike(String value) {
            addCriterion("ret_summary not like", value, "retSummary");
            return (Criteria) this;
        }

        public Criteria andRetSummaryIn(List<String> values) {
            addCriterion("ret_summary in", values, "retSummary");
            return (Criteria) this;
        }

        public Criteria andRetSummaryNotIn(List<String> values) {
            addCriterion("ret_summary not in", values, "retSummary");
            return (Criteria) this;
        }

        public Criteria andRetSummaryBetween(String value1, String value2) {
            addCriterion("ret_summary between", value1, value2, "retSummary");
            return (Criteria) this;
        }

        public Criteria andRetSummaryNotBetween(String value1, String value2) {
            addCriterion("ret_summary not between", value1, value2, "retSummary");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}