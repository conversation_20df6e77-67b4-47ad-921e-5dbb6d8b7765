package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.GalleryDO;
import ai.conrain.aigc.platform.dal.example.GalleryExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface GalleryDAO {
    long countByExample(GalleryExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(GalleryDO record);

    int insertSelective(GalleryDO record);

    List<GalleryDO> selectByExampleWithBLOBs(GalleryExample example);

    List<GalleryDO> selectByExample(GalleryExample example);

    GalleryDO selectByPrimaryKey(Integer id);

    GalleryDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") GalleryDO record, @Param("example") GalleryExample example);

    int updateByExampleWithBLOBs(@Param("record") GalleryDO record, @Param("example") GalleryExample example);

    int updateByExample(@Param("record") GalleryDO record, @Param("example") GalleryExample example);

    int updateByPrimaryKeySelective(GalleryDO record);

    int updateByPrimaryKeyWithBLOBs(GalleryDO record);

    int updateByPrimaryKey(GalleryDO record);

    int logicalDeleteByExample(@Param("example") GalleryExample example);

    int logicalDeleteByPrimaryKey(Integer id);
}