<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionDAO">
    <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="image_id" jdbcType="INTEGER" property="imageId"/>
        <result column="caption" jdbcType="OTHER" property="caption"/>
        <result column="caption_version" jdbcType="VARCHAR" property="captionVersion"/>
        <result column="image_type" jdbcType="VARCHAR" property="imageType"/>
        <result column="cloth_gender_type" jdbcType="VARCHAR" property="clothGenderType"/>
        <result column="cloth_style_similarity" jdbcType="DOUBLE" property="clothStyleSimilarity"/>
        <result column="age_group" jdbcType="VARCHAR" property="ageGroup"/>
        <result column="genre" jdbcType="VARCHAR" property="genre"/>
        <result column="quality_score" jdbcType="DOUBLE" property="qualityScore"/>
        <result column="img_emb" jdbcType="OTHER" property="imgEmb"/>
        <result column="bg_img_emb" jdbcType="OTHER" property="bgImgEmb"/>
        <result column="model_facial_img_emb" jdbcType="OTHER" property="modelFacialImgEmb"/>
        <result column="model_pose_img_emb" jdbcType="OTHER" property="modelPoseImgEmb"/>
        <result column="cloth_style_text_emb" jdbcType="OTHER" property="clothStyleTextEmb"/>
        <result column="cloth_text_emb" jdbcType="OTHER" property="clothTextEmb"/>
        <result column="bg_text_emb" jdbcType="OTHER" property="bgTextEmb"/>
        <result column="accessories_text_emb" jdbcType="OTHER" property="accessoriesTextEmb"/>
        <result column="hairstyle_text_emb" jdbcType="OTHER" property="hairstyleTextEmb"/>
        <result column="pose_text_emb" jdbcType="OTHER" property="poseTextEmb"/>
        <result column="sort_bg_text_emb" jdbcType="OTHER" property="sortBgTextEmb"/>
        <result column="sort_facial_expression_text_emb" jdbcType="OTHER" property="sortFacialExpressionTextEmb"/>
        <result column="sort_accessories_text_emb" jdbcType="OTHER" property="sortAccessoriesTextEmb"/>
        <result column="sort_pose_text_emb" jdbcType="OTHER" property="sortPoseTextEmb"/>
        <result column="ext_info" jdbcType="VARCHAR" property="extInfo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Simple_Column_List">
        id, image_id, caption, caption_version, ext_info, create_time, modify_time,
        deleted, image_type, cloth_gender_type, age_group, quality_score,  genre
    </sql>
    <sql id="Base_Column_List">
        id, image_id, caption, caption_version, img_emb, bg_img_emb, model_facial_img_emb,
        model_pose_img_emb, cloth_style_text_emb, cloth_text_emb, bg_text_emb, accessories_text_emb,
        hairstyle_text_emb, pose_text_emb, sort_bg_text_emb, sort_facial_expression_text_emb,
        sort_accessories_text_emb, sort_pose_text_emb, ext_info, create_time, modify_time,
        deleted, image_type, cloth_gender_type, age_group, quality_score,  genre
    </sql>
    <sql id="Recall_Vector_Column_List">
        id, image_id, caption, caption_version, cloth_style_text_emb, cloth_text_emb, bg_text_emb, accessories_text_emb,
        hairstyle_text_emb, pose_text_emb, ext_info, create_time, modify_time,
        deleted, image_type, cloth_gender_type, age_group, quality_score,  genre
    </sql>
    <sql id="Sort_Vector_Column_List">
        id, image_id, caption, caption_version, img_emb, bg_img_emb, model_facial_img_emb,
        model_pose_img_emb, sort_bg_text_emb, sort_facial_expression_text_emb,
        sort_accessories_text_emb, sort_pose_text_emb, ext_info, create_time, modify_time,
        deleted, image_type, cloth_gender_type, age_group, quality_score,  genre
    </sql>

    <select id="selectSimpleByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageCaptionExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Simple_Column_List"/>
        from image_caption
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                LIMIT ${rows} OFFSET ${offset}
            </if>
            <if test="offset == null">
                LIMIT ${rows}
            </if>
        </if>
    </select>

    <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageCaptionExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from image_caption
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                LIMIT ${rows} OFFSET ${offset}
            </if>
            <if test="offset == null">
                LIMIT ${rows}
            </if>
        </if>
    </select>
    <select id="selectRecallVectorsByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageCaptionExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Recall_Vector_Column_List"/>
        from image_caption
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="rows != null">
            <if test="offset != null">
                LIMIT ${rows} OFFSET ${offset}
            </if>
            <if test="offset == null">
                LIMIT ${rows}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from image_caption
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from image_caption
        where id = #{id,jdbcType=INTEGER}
        and deleted =
        <choose>
            <when test="andLogicalDeleted">
                true
            </when>
            <otherwise>
                false
            </otherwise>
        </choose>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from image_caption
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO" useGeneratedKeys="true">
        insert into image_caption (image_id, caption, caption_version,
        img_emb, bg_img_emb, model_facial_img_emb,
        model_pose_img_emb, cloth_style_text_emb, cloth_text_emb,
        bg_text_emb, accessories_text_emb, hairstyle_text_emb,
        pose_text_emb, sort_bg_text_emb, sort_facial_expression_text_emb,
        sort_accessories_text_emb, sort_pose_text_emb, ext_info,
        create_time, modify_time, deleted,
        image_type, cloth_gender_type, age_group, quality_score,  genre)
        values (#{imageId,jdbcType=INTEGER}, #{caption,jdbcType=OTHER}, #{captionVersion,jdbcType=VARCHAR},
        #{imgEmb,jdbcType=OTHER}, #{bgImgEmb,jdbcType=OTHER}, #{modelFacialImgEmb,jdbcType=OTHER},
        #{modelPoseImgEmb,jdbcType=OTHER}, #{clothStyleTextEmb,jdbcType=OTHER}, #{clothTextEmb,jdbcType=OTHER},
        #{bgTextEmb,jdbcType=OTHER}, #{accessoriesTextEmb,jdbcType=OTHER}, #{hairstyleTextEmb,jdbcType=OTHER},
        #{poseTextEmb,jdbcType=OTHER}, #{sortBgTextEmb,jdbcType=OTHER}, #{sortFacialExpressionTextEmb,jdbcType=OTHER},
        #{sortAccessoriesTextEmb,jdbcType=OTHER}, #{sortPoseTextEmb,jdbcType=OTHER}, #{extInfo,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT},
        #{imageType,jdbcType=VARCHAR}, #{clothGenderType,jdbcType=VARCHAR}, #{ageGroup,jdbcType=VARCHAR}, #{qualityScore,jdbcType=DOUBLE}, #{genre,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO" useGeneratedKeys="true">
        insert into image_caption
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imageId != null">
                image_id,
            </if>
            <if test="caption != null">
                caption,
            </if>
            <if test="captionVersion != null">
                caption_version,
            </if>
            <if test="imgEmb != null">
                img_emb,
            </if>
            <if test="bgImgEmb != null">
                bg_img_emb,
            </if>
            <if test="modelFacialImgEmb != null">
                model_facial_img_emb,
            </if>
            <if test="modelPoseImgEmb != null">
                model_pose_img_emb,
            </if>
            <if test="clothStyleTextEmb != null">
                cloth_style_text_emb,
            </if>
            <if test="clothTextEmb != null">
                cloth_text_emb,
            </if>
            <if test="bgTextEmb != null">
                bg_text_emb,
            </if>
            <if test="accessoriesTextEmb != null">
                accessories_text_emb,
            </if>
            <if test="hairstyleTextEmb != null">
                hairstyle_text_emb,
            </if>
            <if test="poseTextEmb != null">
                pose_text_emb,
            </if>
            <if test="sortBgTextEmb != null">
                sort_bg_text_emb,
            </if>
            <if test="sortFacialExpressionTextEmb != null">
                sort_facial_expression_text_emb,
            </if>
            <if test="sortAccessoriesTextEmb != null">
                sort_accessories_text_emb,
            </if>
            <if test="sortPoseTextEmb != null">
                sort_pose_text_emb,
            </if>
            <if test="extInfo != null">
                ext_info,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="imageType != null">
                image_type,
            </if>
            <if test="clothGenderType != null">
                cloth_gender_type,
            </if>
            <if test="ageGroup != null">
                age_group,
            </if>
            <if test="qualityScore != null">
                quality_score,
            </if>
            <if test="genre != null">
                genre,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imageId != null">
                #{imageId,jdbcType=INTEGER},
            </if>
            <if test="caption != null">
                #{caption,jdbcType=OTHER},
            </if>
            <if test="captionVersion != null">
                #{captionVersion,jdbcType=VARCHAR},
            </if>
            <if test="imgEmb != null">
                #{imgEmb,jdbcType=OTHER},
            </if>
            <if test="bgImgEmb != null">
                #{bgImgEmb,jdbcType=OTHER},
            </if>
            <if test="modelFacialImgEmb != null">
                #{modelFacialImgEmb,jdbcType=OTHER},
            </if>
            <if test="modelPoseImgEmb != null">
                #{modelPoseImgEmb,jdbcType=OTHER},
            </if>
            <if test="clothStyleTextEmb != null">
                #{clothStyleTextEmb,jdbcType=OTHER},
            </if>
            <if test="clothTextEmb != null">
                #{clothTextEmb,jdbcType=OTHER},
            </if>
            <if test="bgTextEmb != null">
                #{bgTextEmb,jdbcType=OTHER},
            </if>
            <if test="accessoriesTextEmb != null">
                #{accessoriesTextEmb,jdbcType=OTHER},
            </if>
            <if test="hairstyleTextEmb != null">
                #{hairstyleTextEmb,jdbcType=OTHER},
            </if>
            <if test="poseTextEmb != null">
                #{poseTextEmb,jdbcType=OTHER},
            </if>
            <if test="sortBgTextEmb != null">
                #{sortBgTextEmb,jdbcType=OTHER},
            </if>
            <if test="sortFacialExpressionTextEmb != null">
                #{sortFacialExpressionTextEmb,jdbcType=OTHER},
            </if>
            <if test="sortAccessoriesTextEmb != null">
                #{sortAccessoriesTextEmb,jdbcType=OTHER},
            </if>
            <if test="sortPoseTextEmb != null">
                #{sortPoseTextEmb,jdbcType=OTHER},
            </if>
            <if test="extInfo != null">
                #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="imageType != null">
                #{imageType,jdbcType=VARCHAR},
            </if>
            <if test="clothGenderType != null">
                #{clothGenderType,jdbcType=VARCHAR},
            </if>
            <if test="ageGroup != null">
                #{ageGroup,jdbcType=VARCHAR},
            </if>
            <if test="qualityScore != null">
                #{qualityScore,jdbcType=DOUBLE},
            </if>
            <if test="genre != null">
                #{genre,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageCaptionExample"
            resultType="java.lang.Long">
        select count(*) from image_caption
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update image_caption
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.imageId != null">
                image_id = #{record.imageId,jdbcType=INTEGER},
            </if>
            <if test="record.caption != null">
                caption = #{record.caption,jdbcType=OTHER},
            </if>
            <if test="record.captionVersion != null">
                caption_version = #{record.captionVersion,jdbcType=VARCHAR},
            </if>
            <if test="record.imgEmb != null">
                img_emb = #{record.imgEmb,jdbcType=OTHER},
            </if>
            <if test="record.bgImgEmb != null">
                bg_img_emb = #{record.bgImgEmb,jdbcType=OTHER},
            </if>
            <if test="record.modelFacialImgEmb != null">
                model_facial_img_emb = #{record.modelFacialImgEmb,jdbcType=OTHER},
            </if>
            <if test="record.modelPoseImgEmb != null">
                model_pose_img_emb = #{record.modelPoseImgEmb,jdbcType=OTHER},
            </if>
            <if test="record.clothStyleTextEmb != null">
                cloth_style_text_emb = #{record.clothStyleTextEmb,jdbcType=OTHER},
            </if>
            <if test="record.clothTextEmb != null">
                cloth_text_emb = #{record.clothTextEmb,jdbcType=OTHER},
            </if>
            <if test="record.bgTextEmb != null">
                bg_text_emb = #{record.bgTextEmb,jdbcType=OTHER},
            </if>
            <if test="record.accessoriesTextEmb != null">
                accessories_text_emb = #{record.accessoriesTextEmb,jdbcType=OTHER},
            </if>
            <if test="record.hairstyleTextEmb != null">
                hairstyle_text_emb = #{record.hairstyleTextEmb,jdbcType=OTHER},
            </if>
            <if test="record.poseTextEmb != null">
                pose_text_emb = #{record.poseTextEmb,jdbcType=OTHER},
            </if>
            <if test="record.sortBgTextEmb != null">
                sort_bg_text_emb = #{record.sortBgTextEmb,jdbcType=OTHER},
            </if>
            <if test="record.sortFacialExpressionTextEmb != null">
                sort_facial_expression_text_emb = #{record.sortFacialExpressionTextEmb,jdbcType=OTHER},
            </if>
            <if test="record.sortAccessoriesTextEmb != null">
                sort_accessories_text_emb = #{record.sortAccessoriesTextEmb,jdbcType=OTHER},
            </if>
            <if test="record.sortPoseTextEmb != null">
                sort_pose_text_emb = #{record.sortPoseTextEmb,jdbcType=OTHER},
            </if>
            <if test="record.extInfo != null">
                ext_info = #{record.extInfo,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.modifyTime != null">
                modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.deleted != null">
                deleted = #{record.deleted,jdbcType=BIT},
            </if>
            <if test="record.imageType != null">
                image_type = #{record.imageType,jdbcType=VARCHAR},
            </if>
            <if test="record.clothGenderType != null">
                cloth_gender_type = #{record.clothGenderType,jdbcType=VARCHAR},
            </if>
            <if test="record.ageGroup != null">
                age_group = #{record.ageGroup,jdbcType=VARCHAR},
            </if>
            <if test="record.qualityScore != null">
                quality_score = #{record.qualityScore,jdbcType=DOUBLE},
            </if>
            <if test="record.genre != null">
                genre = #{record.genre,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update image_caption
        set id = #{record.id,jdbcType=INTEGER},
        image_id = #{record.imageId,jdbcType=INTEGER},
        caption = #{record.caption,jdbcType=OTHER},
        caption_version = #{record.captionVersion,jdbcType=VARCHAR},
        img_emb = #{record.imgEmb,jdbcType=OTHER},
        bg_img_emb = #{record.bgImgEmb,jdbcType=OTHER},
        model_facial_img_emb = #{record.modelFacialImgEmb,jdbcType=OTHER},
        model_pose_img_emb = #{record.modelPoseImgEmb,jdbcType=OTHER},
        cloth_style_text_emb = #{record.clothStyleTextEmb,jdbcType=OTHER},
        cloth_text_emb = #{record.clothTextEmb,jdbcType=OTHER},
        bg_text_emb = #{record.bgTextEmb,jdbcType=OTHER},
        accessories_text_emb = #{record.accessoriesTextEmb,jdbcType=OTHER},
        hairstyle_text_emb = #{record.hairstyleTextEmb,jdbcType=OTHER},
        pose_text_emb = #{record.poseTextEmb,jdbcType=OTHER},
        sort_bg_text_emb = #{record.sortBgTextEmb,jdbcType=OTHER},
        sort_facial_expression_text_emb = #{record.sortFacialExpressionTextEmb,jdbcType=OTHER},
        sort_accessories_text_emb = #{record.sortAccessoriesTextEmb,jdbcType=OTHER},
        sort_pose_text_emb = #{record.sortPoseTextEmb,jdbcType=OTHER},
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
        deleted = #{record.deleted,jdbcType=BIT},
        image_type = #{record.imageType,jdbcType=VARCHAR},
        cloth_gender_type = #{record.clothGenderType,jdbcType=VARCHAR},
        age_group = #{record.ageGroup,jdbcType=VARCHAR},
        quality_score = #{record.qualityScore,jdbcType=DOUBLE},
        genre = #{record.genre,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO">
        update image_caption
        <set>
            <if test="imageId != null">
                image_id = #{imageId,jdbcType=INTEGER},
            </if>
            <if test="caption != null">
                caption = #{caption,jdbcType=OTHER},
            </if>
            <if test="captionVersion != null">
                caption_version = #{captionVersion,jdbcType=VARCHAR},
            </if>
            <if test="imgEmb != null">
                img_emb = #{imgEmb,jdbcType=OTHER},
            </if>
            <if test="bgImgEmb != null">
                bg_img_emb = #{bgImgEmb,jdbcType=OTHER},
            </if>
            <if test="modelFacialImgEmb != null">
                model_facial_img_emb = #{modelFacialImgEmb,jdbcType=OTHER},
            </if>
            <if test="modelPoseImgEmb != null">
                model_pose_img_emb = #{modelPoseImgEmb,jdbcType=OTHER},
            </if>
            <if test="clothStyleTextEmb != null">
                cloth_style_text_emb = #{clothStyleTextEmb,jdbcType=OTHER},
            </if>
            <if test="clothTextEmb != null">
                cloth_text_emb = #{clothTextEmb,jdbcType=OTHER},
            </if>
            <if test="bgTextEmb != null">
                bg_text_emb = #{bgTextEmb,jdbcType=OTHER},
            </if>
            <if test="accessoriesTextEmb != null">
                accessories_text_emb = #{accessoriesTextEmb,jdbcType=OTHER},
            </if>
            <if test="hairstyleTextEmb != null">
                hairstyle_text_emb = #{hairstyleTextEmb,jdbcType=OTHER},
            </if>
            <if test="poseTextEmb != null">
                pose_text_emb = #{poseTextEmb,jdbcType=OTHER},
            </if>
            <if test="sortBgTextEmb != null">
                sort_bg_text_emb = #{sortBgTextEmb,jdbcType=OTHER},
            </if>
            <if test="sortFacialExpressionTextEmb != null">
                sort_facial_expression_text_emb = #{sortFacialExpressionTextEmb,jdbcType=OTHER},
            </if>
            <if test="sortAccessoriesTextEmb != null">
                sort_accessories_text_emb = #{sortAccessoriesTextEmb,jdbcType=OTHER},
            </if>
            <if test="sortPoseTextEmb != null">
                sort_pose_text_emb = #{sortPoseTextEmb,jdbcType=OTHER},
            </if>
            <if test="extInfo != null">
                ext_info = #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="imageType != null">
                image_type = #{imageType,jdbcType=VARCHAR},
            </if>
            <if test="clothGenderType != null">
                cloth_gender_type = #{clothGenderType,jdbcType=VARCHAR},
            </if>
            <if test="ageGroup != null">
                age_group = #{ageGroup,jdbcType=VARCHAR},
            </if>
            <if test="qualityScore != null">
                quality_score = #{qualityScore,jdbcType=DOUBLE},
            </if>
            <if test="genre != null">
                genre = #{genre,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO">
        update image_caption
        set image_id = #{imageId,jdbcType=INTEGER},
        caption = #{caption,jdbcType=OTHER},
        caption_version = #{captionVersion,jdbcType=VARCHAR},
        img_emb = #{imgEmb,jdbcType=OTHER},
        bg_img_emb = #{bgImgEmb,jdbcType=OTHER},
        model_facial_img_emb = #{modelFacialImgEmb,jdbcType=OTHER},
        model_pose_img_emb = #{modelPoseImgEmb,jdbcType=OTHER},
        cloth_style_text_emb = #{clothStyleTextEmb,jdbcType=OTHER},
        cloth_text_emb = #{clothTextEmb,jdbcType=OTHER},
        bg_text_emb = #{bgTextEmb,jdbcType=OTHER},
        accessories_text_emb = #{accessoriesTextEmb,jdbcType=OTHER},
        hairstyle_text_emb = #{hairstyleTextEmb,jdbcType=OTHER},
        pose_text_emb = #{poseTextEmb,jdbcType=OTHER},
        sort_bg_text_emb = #{sortBgTextEmb,jdbcType=OTHER},
        sort_facial_expression_text_emb = #{sortFacialExpressionTextEmb,jdbcType=OTHER},
        sort_accessories_text_emb = #{sortAccessoriesTextEmb,jdbcType=OTHER},
        sort_pose_text_emb = #{sortPoseTextEmb,jdbcType=OTHER},
        ext_info = #{extInfo,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        deleted = #{deleted,jdbcType=BIT},
        image_type = #{imageType,jdbcType=VARCHAR},
        cloth_gender_type = #{clothGenderType,jdbcType=VARCHAR},
        age_group = #{ageGroup,jdbcType=VARCHAR},
        quality_score = #{qualityScore,jdbcType=DOUBLE},
        genre = #{genre,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="logicalDeleteByExample" parameterType="map">
        update image_caption set deleted = true
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
        update image_caption set deleted = true
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByStyleVector" resultMap="BaseResultMap">
        set local hnsw.ef_search=600;

        SELECT<include refid="Recall_Vector_Column_List"/>, 1 - ((cloth_style_text_emb::vector(256)) &lt;=&gt;(#{styleVector}::vector(256))) as cloth_style_similarity
        FROM image_caption
        WHERE (cloth_gender_type = #{gender} or cloth_gender_type is null or cloth_gender_type = 'Unknown') and image_type='scene' and deleted = false
        <if test="ageGroup != null and ageGroup != 'Unknown'">
            and age_group=#{ageGroup}
        </if>
        <if test="genre != null">
            and COALESCE(genre, caption::jsonb->'Shooting_Theme'->>'Intended_Use') = #{genre}
        </if>
        <if test="excludeImageCaptionIds != null and excludeImageCaptionIds.size() > 0">
            and id not in
            <foreach collection="excludeImageCaptionIds" item="excludeId" open="(" separator="," close=")">
                #{excludeId}
            </foreach>
        </if>
        and (cloth_style_text_emb::vector(256)) &lt;=&gt; (#{styleVector}::vector(256)) &lt; (1-#{similarityThreshold})
        and quality_score &gt;= 0.5
        ORDER BY (cloth_style_text_emb::vector(256)) &lt;=&gt; (#{styleVector}::vector(256))
        LIMIT #{limit};
    </select>

    <select id="selectSortVectors" resultMap="BaseResultMap">
        SELECT
        <include refid="Sort_Vector_Column_List"/>
        from image_caption
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectNoQualityScoreImageCaptionIds" resultMap="BaseResultMap">
        SELECT id,image_id FROM image_caption WHERE quality_score IS NULL limit 30
    </select>

</mapper>