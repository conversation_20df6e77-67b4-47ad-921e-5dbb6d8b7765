<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.SearchResultImgDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.SearchResultImgDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="search_id" jdbcType="INTEGER" property="searchId" />
    <result column="image_id" jdbcType="INTEGER" property="imageId" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="image_show_url" jdbcType="VARCHAR" property="imageShowUrl" />
    <result column="image_caption_id" jdbcType="INTEGER" property="imageCaptionId" />
    <result column="genre" jdbcType="VARCHAR" property="genre" />
    <result column="bg_cluster_key" jdbcType="VARCHAR" property="bgClusterKey" />
    <result column="idx_in_cluster" jdbcType="INTEGER" property="idxInCluster" />
    <result column="style_similarity" jdbcType="DOUBLE" property="styleSimilarity" />
    <result column="match_score" jdbcType="DOUBLE" property="matchScore" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, search_id, image_id, image_url, image_show_url, image_caption_id, genre, bg_cluster_key, 
    idx_in_cluster, style_similarity, match_score, ext_info, create_time, modify_time
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.SearchResultImgExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from search_result_img
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        LIMIT ${rows} OFFSET ${offset}
      </if>
      <if test="offset == null">
        LIMIT ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from search_result_img
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from search_result_img
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.SearchResultImgDO" useGeneratedKeys="true">
    insert into search_result_img (search_id, image_id, image_url, 
      image_show_url, image_caption_id, genre, 
      bg_cluster_key, idx_in_cluster, style_similarity, 
      match_score, ext_info, create_time, 
      modify_time)
    values (#{searchId,jdbcType=INTEGER}, #{imageId,jdbcType=INTEGER}, #{imageUrl,jdbcType=VARCHAR}, 
      #{imageShowUrl,jdbcType=VARCHAR}, #{imageCaptionId,jdbcType=INTEGER}, #{genre,jdbcType=VARCHAR}, 
      #{bgClusterKey,jdbcType=VARCHAR}, #{idxInCluster,jdbcType=INTEGER}, #{styleSimilarity,jdbcType=DOUBLE}, 
      #{matchScore,jdbcType=DOUBLE}, #{extInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.SearchResultImgDO" useGeneratedKeys="true">
    insert into search_result_img
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="searchId != null">
        search_id,
      </if>
      <if test="imageId != null">
        image_id,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="imageShowUrl != null">
        image_show_url,
      </if>
      <if test="imageCaptionId != null">
        image_caption_id,
      </if>
      <if test="genre != null">
        genre,
      </if>
      <if test="bgClusterKey != null">
        bg_cluster_key,
      </if>
      <if test="idxInCluster != null">
        idx_in_cluster,
      </if>
      <if test="styleSimilarity != null">
        style_similarity,
      </if>
      <if test="matchScore != null">
        match_score,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="searchId != null">
        #{searchId,jdbcType=INTEGER},
      </if>
      <if test="imageId != null">
        #{imageId,jdbcType=INTEGER},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageShowUrl != null">
        #{imageShowUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageCaptionId != null">
        #{imageCaptionId,jdbcType=INTEGER},
      </if>
      <if test="genre != null">
        #{genre,jdbcType=VARCHAR},
      </if>
      <if test="bgClusterKey != null">
        #{bgClusterKey,jdbcType=VARCHAR},
      </if>
      <if test="idxInCluster != null">
        #{idxInCluster,jdbcType=INTEGER},
      </if>
      <if test="styleSimilarity != null">
        #{styleSimilarity,jdbcType=DOUBLE},
      </if>
      <if test="matchScore != null">
        #{matchScore,jdbcType=DOUBLE},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.SearchResultImgExample" resultType="java.lang.Long">
    select count(*) from search_result_img
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update search_result_img
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.searchId != null">
        search_id = #{record.searchId,jdbcType=INTEGER},
      </if>
      <if test="record.imageId != null">
        image_id = #{record.imageId,jdbcType=INTEGER},
      </if>
      <if test="record.imageUrl != null">
        image_url = #{record.imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.imageShowUrl != null">
        image_show_url = #{record.imageShowUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.imageCaptionId != null">
        image_caption_id = #{record.imageCaptionId,jdbcType=INTEGER},
      </if>
      <if test="record.genre != null">
        genre = #{record.genre,jdbcType=VARCHAR},
      </if>
      <if test="record.bgClusterKey != null">
        bg_cluster_key = #{record.bgClusterKey,jdbcType=VARCHAR},
      </if>
      <if test="record.idxInCluster != null">
        idx_in_cluster = #{record.idxInCluster,jdbcType=INTEGER},
      </if>
      <if test="record.styleSimilarity != null">
        style_similarity = #{record.styleSimilarity,jdbcType=DOUBLE},
      </if>
      <if test="record.matchScore != null">
        match_score = #{record.matchScore,jdbcType=DOUBLE},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update search_result_img
    set id = #{record.id,jdbcType=INTEGER},
      search_id = #{record.searchId,jdbcType=INTEGER},
      image_id = #{record.imageId,jdbcType=INTEGER},
      image_url = #{record.imageUrl,jdbcType=VARCHAR},
      image_show_url = #{record.imageShowUrl,jdbcType=VARCHAR},
      image_caption_id = #{record.imageCaptionId,jdbcType=INTEGER},
      genre = #{record.genre,jdbcType=VARCHAR},
      bg_cluster_key = #{record.bgClusterKey,jdbcType=VARCHAR},
      idx_in_cluster = #{record.idxInCluster,jdbcType=INTEGER},
      style_similarity = #{record.styleSimilarity,jdbcType=DOUBLE},
      match_score = #{record.matchScore,jdbcType=DOUBLE},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.SearchResultImgDO">
    update search_result_img
    <set>
      <if test="searchId != null">
        search_id = #{searchId,jdbcType=INTEGER},
      </if>
      <if test="imageId != null">
        image_id = #{imageId,jdbcType=INTEGER},
      </if>
      <if test="imageUrl != null">
        image_url = #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageShowUrl != null">
        image_show_url = #{imageShowUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageCaptionId != null">
        image_caption_id = #{imageCaptionId,jdbcType=INTEGER},
      </if>
      <if test="genre != null">
        genre = #{genre,jdbcType=VARCHAR},
      </if>
      <if test="bgClusterKey != null">
        bg_cluster_key = #{bgClusterKey,jdbcType=VARCHAR},
      </if>
      <if test="idxInCluster != null">
        idx_in_cluster = #{idxInCluster,jdbcType=INTEGER},
      </if>
      <if test="styleSimilarity != null">
        style_similarity = #{styleSimilarity,jdbcType=DOUBLE},
      </if>
      <if test="matchScore != null">
        match_score = #{matchScore,jdbcType=DOUBLE},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.SearchResultImgDO">
    update search_result_img
    set search_id = #{searchId,jdbcType=INTEGER},
      image_id = #{imageId,jdbcType=INTEGER},
      image_url = #{imageUrl,jdbcType=VARCHAR},
      image_show_url = #{imageShowUrl,jdbcType=VARCHAR},
      image_caption_id = #{imageCaptionId,jdbcType=INTEGER},
      genre = #{genre,jdbcType=VARCHAR},
      bg_cluster_key = #{bgClusterKey,jdbcType=VARCHAR},
      idx_in_cluster = #{idxInCluster,jdbcType=INTEGER},
      style_similarity = #{styleSimilarity,jdbcType=DOUBLE},
      match_score = #{matchScore,jdbcType=DOUBLE},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- 批量插入搜索结果图片 -->
  <insert id="batchInsert" parameterType="java.util.List">
    insert into search_result_img (
      search_id, image_id, image_url, image_show_url, image_caption_id, genre, bg_cluster_key,
      idx_in_cluster, style_similarity, match_score, ext_info, create_time, modify_time
    ) values 
    <foreach collection="records" item="record" separator=",">
      (
        #{record.searchId,jdbcType=INTEGER}, 
        #{record.imageId,jdbcType=INTEGER},
        #{record.imageUrl,jdbcType=VARCHAR}, 
        #{record.imageShowUrl,jdbcType=VARCHAR}, 
        #{record.imageCaptionId,jdbcType=INTEGER}, 
        #{record.genre,jdbcType=VARCHAR}, 
        #{record.bgClusterKey,jdbcType=VARCHAR},
        #{record.idxInCluster,jdbcType=INTEGER},
        #{record.styleSimilarity,jdbcType=DOUBLE}, 
        #{record.matchScore,jdbcType=DOUBLE}, 
        #{record.extInfo,jdbcType=VARCHAR}, 
        #{record.createTime,jdbcType=TIMESTAMP}, 
        #{record.modifyTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
</mapper>