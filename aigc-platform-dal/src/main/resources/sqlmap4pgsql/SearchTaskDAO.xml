<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.SearchTaskDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.SearchTaskDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_session_id" jdbcType="INTEGER" property="userSessionId" />
    <result column="user_session_task_id" jdbcType="INTEGER" property="userSessionTaskId" />
    <result column="cloth_img_detail" jdbcType="VARCHAR" property="clothImgDetail" />
    <result column="cloth_analysis" jdbcType="VARCHAR" property="clothAnalysis" />
    <result column="ref_img_detail" jdbcType="VARCHAR" property="refImgDetail" />
    <result column="ref_img_analysis" jdbcType="VARCHAR" property="refImgAnalysis" />
    <result column="search_options" jdbcType="VARCHAR" property="searchOptions" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="ret_summary" jdbcType="VARCHAR" property="retSummary" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, user_session_id, user_session_task_id, cloth_img_detail, cloth_analysis, 
    ref_img_detail, ref_img_analysis, search_options, status, ret_summary, ext_info, 
    create_time, modify_time
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.SearchTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from search_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        LIMIT ${rows} OFFSET ${offset}
      </if>
      <if test="offset == null">
        LIMIT ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from search_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from search_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.SearchTaskDO" useGeneratedKeys="true">
    insert into search_task (user_id, user_session_id, user_session_task_id, 
      cloth_img_detail, cloth_analysis, ref_img_detail, 
      ref_img_analysis, search_options, status, 
      ret_summary, ext_info, create_time, 
      modify_time)
    values (#{userId,jdbcType=INTEGER}, #{userSessionId,jdbcType=INTEGER}, #{userSessionTaskId,jdbcType=INTEGER}, 
      #{clothImgDetail,jdbcType=VARCHAR}, #{clothAnalysis,jdbcType=VARCHAR}, #{refImgDetail,jdbcType=VARCHAR}, 
      #{refImgAnalysis,jdbcType=VARCHAR}, #{searchOptions,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{retSummary,jdbcType=VARCHAR}, #{extInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.SearchTaskDO" useGeneratedKeys="true">
    insert into search_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="userSessionId != null">
        user_session_id,
      </if>
      <if test="userSessionTaskId != null">
        user_session_task_id,
      </if>
      <if test="clothImgDetail != null">
        cloth_img_detail,
      </if>
      <if test="clothAnalysis != null">
        cloth_analysis,
      </if>
      <if test="refImgDetail != null">
        ref_img_detail,
      </if>
      <if test="refImgAnalysis != null">
        ref_img_analysis,
      </if>
      <if test="searchOptions != null">
        search_options,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="retSummary != null">
        ret_summary,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="userSessionId != null">
        #{userSessionId,jdbcType=INTEGER},
      </if>
      <if test="userSessionTaskId != null">
        #{userSessionTaskId,jdbcType=INTEGER},
      </if>
      <if test="clothImgDetail != null">
        #{clothImgDetail,jdbcType=VARCHAR},
      </if>
      <if test="clothAnalysis != null">
        #{clothAnalysis,jdbcType=VARCHAR},
      </if>
      <if test="refImgDetail != null">
        #{refImgDetail,jdbcType=VARCHAR},
      </if>
      <if test="refImgAnalysis != null">
        #{refImgAnalysis,jdbcType=VARCHAR},
      </if>
      <if test="searchOptions != null">
        #{searchOptions,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="retSummary != null">
        #{retSummary,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.SearchTaskExample" resultType="java.lang.Long">
    select count(*) from search_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update search_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.userSessionId != null">
        user_session_id = #{record.userSessionId,jdbcType=INTEGER},
      </if>
      <if test="record.userSessionTaskId != null">
        user_session_task_id = #{record.userSessionTaskId,jdbcType=INTEGER},
      </if>
      <if test="record.clothImgDetail != null">
        cloth_img_detail = #{record.clothImgDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.clothAnalysis != null">
        cloth_analysis = #{record.clothAnalysis,jdbcType=VARCHAR},
      </if>
      <if test="record.refImgDetail != null">
        ref_img_detail = #{record.refImgDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.refImgAnalysis != null">
        ref_img_analysis = #{record.refImgAnalysis,jdbcType=VARCHAR},
      </if>
      <if test="record.searchOptions != null">
        search_options = #{record.searchOptions,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.retSummary != null">
        ret_summary = #{record.retSummary,jdbcType=VARCHAR},
      </if>
      <if test="record.extInfo != null">
        ext_info = #{record.extInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update search_task
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      user_session_id = #{record.userSessionId,jdbcType=INTEGER},
      user_session_task_id = #{record.userSessionTaskId,jdbcType=INTEGER},
      cloth_img_detail = #{record.clothImgDetail,jdbcType=VARCHAR},
      cloth_analysis = #{record.clothAnalysis,jdbcType=VARCHAR},
      ref_img_detail = #{record.refImgDetail,jdbcType=VARCHAR},
      ref_img_analysis = #{record.refImgAnalysis,jdbcType=VARCHAR},
      search_options = #{record.searchOptions,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      ret_summary = #{record.retSummary,jdbcType=VARCHAR},
      ext_info = #{record.extInfo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.SearchTaskDO">
    update search_task
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="userSessionId != null">
        user_session_id = #{userSessionId,jdbcType=INTEGER},
      </if>
      <if test="userSessionTaskId != null">
        user_session_task_id = #{userSessionTaskId,jdbcType=INTEGER},
      </if>
      <if test="clothImgDetail != null">
        cloth_img_detail = #{clothImgDetail,jdbcType=VARCHAR},
      </if>
      <if test="clothAnalysis != null">
        cloth_analysis = #{clothAnalysis,jdbcType=VARCHAR},
      </if>
      <if test="refImgDetail != null">
        ref_img_detail = #{refImgDetail,jdbcType=VARCHAR},
      </if>
      <if test="refImgAnalysis != null">
        ref_img_analysis = #{refImgAnalysis,jdbcType=VARCHAR},
      </if>
      <if test="searchOptions != null">
        search_options = #{searchOptions,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="retSummary != null">
        ret_summary = #{retSummary,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.SearchTaskDO">
    update search_task
    set user_id = #{userId,jdbcType=INTEGER},
      user_session_id = #{userSessionId,jdbcType=INTEGER},
      user_session_task_id = #{userSessionTaskId,jdbcType=INTEGER},
      cloth_img_detail = #{clothImgDetail,jdbcType=VARCHAR},
      cloth_analysis = #{clothAnalysis,jdbcType=VARCHAR},
      ref_img_detail = #{refImgDetail,jdbcType=VARCHAR},
      ref_img_analysis = #{refImgAnalysis,jdbcType=VARCHAR},
      search_options = #{searchOptions,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      ret_summary = #{retSummary,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>