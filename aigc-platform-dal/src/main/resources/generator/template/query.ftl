package ${queryPackageUrl};

import lombok.Data;
<#list entityJavaImports as im>
import ${im};
</#list>

import java.io.Serializable;

/**
 * ${entityName}Query
 *
 * @version ${entityName}Service.java
 */
@Data
public class ${entityName}Query implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

<#list columns as ci>
<#if ci.column != "deleted" && ci.column != "tenant" && ci.column != "env">
        /** ${ci.comment} */
        private ${ci.javaType} ${ci.property};
</#if>

</#list>
        /** 翻页参数：页面大小 */
        private Integer pageSize;

        /** 翻页参数：页数（从1开始） */
        private Integer pageNum;

        /** 排序指令（示例："id asc"） */
        private String orderBy;

}