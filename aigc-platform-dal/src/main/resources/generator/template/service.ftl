package ${serviceUrl};

import ${queryPackageUrl}.${entityName}Query;
import ${voPackageUrl}.${entityName}VO;
<#if targetRuntime == "MyBatis3">
import ai.conrain.aigc.platform.service.model.common.PageInfo;
</#if>

import java.util.List;

/**
 * ${entityComment} Service定义
 *
 * <AUTHOR>
 * @version ${entityName}Service.java
 */
public interface ${entityName}Service {
	
	/**
	 * 查询${entityComment}对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	${entityName}VO selectById(${idType} id);

<#if enableDeleteByPrimaryKey>
	/**
	 * 删除${entityComment}对象
	 * @param id 主键
	 */
	void deleteById(${idType} id);
</#if>

	/**
	 * 添加${entityComment}对象
	 * @param ${objectName} 对象参数
	 * @return 返回结果
	 */
	${entityName}VO insert(${entityName}VO ${objectName});

<#if targetRuntime == "MyBatis3Simple">
	<#if enableUpdateByPrimaryKey>
	/**
	 * 修改${entityComment}对象
	 * @param ${objectName} 对象参数
	 */
	void updateById(${entityName}VO ${objectName});

	</#if>
	/**
	 * 全量查询
	 * return 结果
	 */
	List<${entityName}VO> findAll();
<#else>
	<#if enableUpdateByPrimaryKey>
	/**
	 * 修改${entityComment}对象
	 * @param ${objectName} 对象参数
	 */
	void updateByIdSelective(${entityName}VO ${objectName});

	</#if>
	/**
	 * 带条件批量查询${entityComment}列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<${entityName}VO> query${entityName}List(${entityName}Query query);

	/**
	 * 带条件查询${entityComment}数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long query${entityName}Count(${entityName}Query query);

	/**
	 * 带条件分页查询${entityComment}
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<${entityName}VO> query${entityName}ByPage(${entityName}Query query);
</#if>
}