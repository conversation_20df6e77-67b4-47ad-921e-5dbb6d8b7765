package ${serviceImplUrl};

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ${entityUrl}.${entityName}DO;
<#if targetRuntime == "MyBatis3">
import ${exampleUrl}.${entityName}Example;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
</#if>
import ${queryPackageUrl}.${entityName}Query;
import ${voPackageUrl}.${entityName}VO;
import ${converterPackageUrl}.${entityName}Converter;
import ${daoUrl}.${entityName}DAO;
import ${serviceUrl}.${entityName}Service;

/**   
 * ${entityName}Service实现
 *
 * <AUTHOR>
 * @version ${entityName}Service.java
 */
@Slf4j
@Service
public class ${entityName}ServiceImpl implements ${entityName}Service {

	/** DAO */
	@Autowired
	private ${entityName}DAO ${objectName}DAO;

	@Override
	public ${entityName}VO selectById(${idType} id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

<#if logicDelete>
		${entityName}DO data = ${objectName}DAO.selectByPrimaryKeyWithLogicalDelete(id, false);
<#else>
		${entityName}DO data = ${objectName}DAO.selectByPrimaryKey(id);
</#if>
		if (null == data) {
			return null;
		}

		return ${entityName}Converter.do2VO(data);
	}

<#if enableDeleteByPrimaryKey>
	@Override
	public void deleteById(${idType} id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

<#if logicDelete>
		int n = ${objectName}DAO.logicalDeleteByPrimaryKey(id);
<#else>
		int n = ${objectName}DAO.deleteByPrimaryKey(id);
</#if>
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除${entityName}失败");
	}
</#if>

	@Override
	public ${entityName}VO insert(${entityName}VO ${objectName}) {
		AssertUtil.assertNotNull(${objectName}, ResultCode.PARAM_INVALID, "${objectName} is null");
		AssertUtil.assertTrue(${objectName}.getId() == null, ResultCode.PARAM_INVALID, "${objectName}.id is present");

		//创建时间、修改时间兜底
		if (${objectName}.getCreateTime() == null) {
			${objectName}.setCreateTime(new Date());
		}

		if (${objectName}.getModifyTime() == null) {
			${objectName}.setModifyTime(new Date());
		}

		${entityName}DO data = ${entityName}Converter.vo2DO(${objectName});
<#if logicDelete>
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
</#if>
		Integer n = ${objectName}DAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建${entityName}失败");
		AssertUtil.assertNotNull(data.getId(), "新建${entityName}返回id为空");
		${objectName}.setId(data.getId());
		return ${objectName};
	}

<#if targetRuntime == "MyBatis3Simple">
<#if enableUpdateByPrimaryKey>
	@Override
	public void updateById(${entityName}VO ${objectName}) {
		AssertUtil.assertNotNull(${objectName}, ResultCode.PARAM_INVALID, "${objectName} is null");
		AssertUtil.assertTrue(${objectName}.getId() != null, ResultCode.PARAM_INVALID, "${objectName}.id is null");
		//修改时间必须更新
		${objectName}.setModifyTime(new Date());

		${entityName}DO data = ${entityName}Converter.vo2DO(${objectName});
<#if logicDelete>
		//逻辑删除标过滤
		data.setDeleted(false);
</#if>
		int n = ${objectName}DAO.updateByPrimaryKey(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新${entityName}失败，影响行数:" + n);
	}
</#if>

	@Override
	public List<${entityName}VO> findAll() {
		List<${entityName}DO> list = ${objectName}DAO.selectAll();
		return ${entityName}Converter.doList2VOList(list);
	}
<#else>

<#if enableUpdateByPrimaryKey>
	@Override
	public void updateByIdSelective(${entityName}VO ${objectName}) {
		AssertUtil.assertNotNull(${objectName}, ResultCode.PARAM_INVALID, "${objectName} is null");
    	AssertUtil.assertTrue(${objectName}.getId() != null, ResultCode.PARAM_INVALID, "${objectName}.id is null");

		//修改时间必须更新
		${objectName}.setModifyTime(new Date());
		${entityName}DO data = ${entityName}Converter.vo2DO(${objectName});
<#if logicDelete>
		//逻辑删除标过滤
		data.setDeleted(false);
</#if>
		int n = ${objectName}DAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新${entityName}失败，影响行数:" + n);
	}
</#if>

	@Override
	public List<${entityName}VO> query${entityName}List(${entityName}Query query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		${entityName}Example example = ${entityName}Converter.query2Example(query);

		List<${entityName}DO> list = ${objectName}DAO.selectByExample(example);
		return ${entityName}Converter.doList2VOList(list);
	}

	@Override
	public Long query${entityName}Count(${entityName}Query query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		${entityName}Example example = ${entityName}Converter.query2Example(query);
		return ${objectName}DAO.countByExample(example);
	}

	/**
	 * 带条件分页查询${entityComment}
	 */
	@Override
	public PageInfo<${entityName}VO> query${entityName}ByPage(${entityName}Query query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<${entityName}VO> page = new PageInfo<>();

		${entityName}Example example = ${entityName}Converter.query2Example(query);
		long totalCount = ${objectName}DAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<${entityName}DO> list = ${objectName}DAO.selectByExample(example);
		page.setList(${entityName}Converter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

</#if>
}