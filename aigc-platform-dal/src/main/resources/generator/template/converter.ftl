package ${converterPackageUrl};

import ${entityUrl}.${entityName}DO;
import ${queryPackageUrl}.${entityName}Query;
<#if targetRuntime == "MyBatis3">
import ${exampleUrl}.${entityName}Example;
</#if>
import ${voPackageUrl}.${entityName}VO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * ${entityName}Converter
 *
 * @version ${entityName}Service.java
 */
public class ${entityName}Converter {

    /**
     * DO -> VO
     */
    public static ${entityName}VO do2VO(${entityName}DO from) {
        ${entityName}VO to = new ${entityName}VO();
    <#list columns as ci>
    	<#if ci.column != "deleted" && ci.column != "tenant" && ci.column != "env">
        to.set${ci.upFiled}(from.get${ci.upFiled}());
        </#if>
    </#list>

        return to;
    }

    /**
     * VO -> DO
     */
    public static ${entityName}DO vo2DO(${entityName}VO from) {
        ${entityName}DO to = new ${entityName}DO();
<#list columns as ci>
<#if ci.column != "deleted" && ci.column != "tenant" && ci.column != "env">
        to.set${ci.upFiled}(from.get${ci.upFiled}());
</#if>
</#list>

        return to;
    }

<#if targetRuntime == "MyBatis3">
    /**
     * Query -> Example
     */
    public static ${entityName}Example query2Example(${entityName}Query from) {
        ${entityName}Example to = new ${entityName}Example();
        ${entityName}Example.Criteria  c = to.createCriteria();

        //各字段条件过滤
<#list columns as ci>
<#if ci.column != "deleted" && ci.jdbcType != "LONGVARCHAR" && ci.column != "tenant" && ci.column != "env">
        if (!ObjectUtils.isEmpty(from.get${ci.upFiled}())) {
            c.and${ci.upFiled}EqualTo(from.get${ci.upFiled}());
        }
</#if>
</#list>
<#if logicDelete>
        //逻辑删除过滤
        for (${entityName}Example.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
</#if>
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
</#if>
    /**
     * do list -> vo list
     */
    public static List<${entityName}VO> doList2VOList(List<${entityName}DO> list) {
        return CommonUtil.listConverter(list, ${entityName}Converter::do2VO);
    }
}