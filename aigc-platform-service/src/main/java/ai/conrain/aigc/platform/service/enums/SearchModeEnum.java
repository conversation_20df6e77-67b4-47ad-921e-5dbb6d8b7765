package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

// 搜索模式, style|more_different|more_with_genre|more_with_bg_cluster
@Getter
public enum SearchModeEnum {
    STYLE("style", "搜索风格图（默认）"),
    MORE_DIFFERENT("more_different", "再推一次"),
    MORE_WITH_GENRE("more_with_genre", "同流派更多推荐"),
    MORE_WITH_BG_CLUSTER("more_with_bg_cluster", "同背景更多推荐"),

    ;

    private final String code;
    private final String desc;

    SearchModeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static SearchModeEnum getByCode(String code) {
        for (SearchModeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
