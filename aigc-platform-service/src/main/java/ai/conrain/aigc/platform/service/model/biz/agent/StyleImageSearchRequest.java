package ai.conrain.aigc.platform.service.model.biz.agent;

import ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum;
import ai.conrain.aigc.platform.service.enums.SearchModeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class StyleImageSearchRequest implements Serializable {

    /** MuseGate用户id */
    private Integer userId;

    /** userSessionId */
    private Integer userSessionId;

    /** userSessionTaskId */
    private Integer userSessionTaskId;

    // 搜索模式, style|more_different|more_with_genre|more_with_bg_cluster
    private SearchModeEnum searchMode;

    // 衣服图片信息
    private ClothImgItem clothImgItem;

    // 用户上传的拍摄风格参考图片
    private List<UserUploadStyleImg> userUploadStyleImgs;

    // 【再推荐一次】排除当前对话所有已经返回的批次，用于完全重新出一批
    private List<Integer> excludeRetInSearchIds;

    // 【基于风格，推荐更多】指定上一次搜索结果，用于基于风格，推荐更多
    private Integer specifiedSearchId;

    // 【基于流派，推荐更多】指定的流派，用于搜索更多指定流派的图，流派里的【更多】：根据指定流派，推荐另外30张图; specifiedGenre非空时，specifiedSearchId不可为空
    private ClothShootGenreEnum specifiedGenre;

    // 【基于套图，推荐更多】指定的风格参考图imageCaptionId，用于基于单张图搜索更多类似; specifiedRetImgCaptionId非空时，specifiedSearchId+specifiedGenre都不可为空
    private Integer specifiedRetImgCaptionId;

    // 召回配置选项
    private RecallOptions recallOptions = new RecallOptions();

    // 多样性配置选项
    private DiversifyOptions diversifyOptions = new DiversifyOptions();

    // 背景聚类配置选项
    private ClusterOptions clusterOptions = new ClusterOptions();
}
