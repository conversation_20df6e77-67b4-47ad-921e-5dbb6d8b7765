package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothTypeConfig;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeTaskConverter;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Slf4j
@Service
public class CreatePoseSampleDiagramCreativeService extends FixedPostureCreationCreativeService {
    @Autowired
    private CreativeElementService creativeElementService;

    @Autowired
    private ComfyUIHelper comfyUIHelper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.POSE_SAMPLE_DIAGRAM;
    }

    @Override
    protected MaterialModelVO fetchModel(AddCreativeRequest request) {
        return mockModelVO();
    }

    @Override
    protected CreativeBatchVO buildData(AddCreativeRequest request, MaterialModelVO modelVO) {
        log.info(
                "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildData::开始构建CreativeBatchVO 批次数据...");
        // 获取批次对象
        CreativeBatchVO creativeBatchVO = super.buildData(request, modelVO);

        // 设置为 ‘示例图创作’ 用户
        creativeBatchVO.setUserId(CommonUtil.mockPostureSampleDiagramContext().getMasterUser());

        // 设置任务数量为1
        creativeBatchVO.setBatchCnt(1);

        // 添加创建图片数量
        creativeBatchVO.addExtInfo(IMAGE_COUNT, request.getPoseSampleDiagram().getImgNum());

        // 设置图片比例
        creativeBatchVO.setImageProportion(request.getPoseSampleDiagram().getProportion());

        // 添加姿势示例图关键字
        creativeBatchVO.addExtInfo(KEY_BIZTAG, POSE_SAMPLE_DIAGRAM);

        // 设置 level 为 3 的场景id
        creativeBatchVO.addExtInfo(KEY_SCENE_ID, request.getPoseSampleDiagram().getCurrentPoseId());
        // 设置CURRENT_POSE_ID
        creativeBatchVO.addExtInfo(CURRENT_POSE_ID, request.getPoseSampleDiagram().getCurrentPoseId());


        log.info(
                "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildData::CreativeBatchVO 批次数据构建完成:{}",
                creativeBatchVO);

        // 返回结果
        return creativeBatchVO;
    }

    @Override
    public List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements) {
        log.info(
                "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildTasks::开始构建CreativeTaskVO 任务数据...");

        // 反向解析参考图
        List<AddCreativeRequest.ReferenceInfo> referenceInfoList = objectMapper.convertValue(
                batch.getExtInfo().get(REFERENCE_INFO_LIST), new TypeReference<List<AddCreativeRequest.ReferenceInfo>>() {
                });

        if (CollectionUtils.isEmpty(referenceInfoList)) {
            return null;
        }


        // 获取参考图配置信息
        AddCreativeRequest.ReferenceInfo referenceInfo = referenceInfoList.get(0);
        if (referenceInfo == null) {
            log.warn("[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildTasks::参考图配置信息为空,跳过姿势:{}", batch.getIntegerFromExtInfo(CURRENT_POSE_ID));
            return new ArrayList<>();
        }


        // 构建任务数据
        List<CreativeTaskVO> result = new ArrayList<>();

        // 构建任务信息
        CreativeTaskVO target = CreativeTaskConverter.request2VO(batch);


        // 填充场景信息
        CreativeElementVO sceneElement = creativeElementService.selectById(referenceInfo.getLoraId());
        elements.add(sceneElement);
        // 填充扩展信息,i为referenceInfo的索引
        fillTaskExt(target, batch, elements, 1);

        // 获取姿势图片地址
        String referenceImageUrl = StringUtils.EMPTY;
        try {

            String imageUrl = referenceInfo.getOriginalImageUrl();
            if (StringUtils.isBlank(imageUrl) && referenceInfo.getReferenceConfig() != null) {
                imageUrl = referenceInfo.getReferenceConfig().getString(CommonConstants.ORIGINAL_IMAGE_URL);
            }

            if (StringUtils.isBlank(imageUrl)) {
                log.error(
                        "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildTasks::原始参考图为空，终止流程");
                throw new BizException(ResultCode.BIZ_FAIL);
            }

            referenceImageUrl = comfyUIHelper.upLoadImage(imageUrl);
        } catch (IOException e) {
            log.error(
                    "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildTasks::图片上传至 ComfyUI 时出现错误::{}",
                    e.getMessage());
            throw new BizException(ResultCode.BIZ_FAIL, "图片上传失败，请稍后重试");
        }


        // 设置 loraId
        target.addExtInfo(KEY_MODEL_ID, referenceInfo.getLoraId());

        //  设置任务类型
        target.setType(getCreativeType());

        // 添加 参考图图片
        target.addExtInfo(CommonConstants.REFERENCE_IMAGE, referenceImageUrl);

        // 添加参考图原图片
        target.addExtInfo(CommonConstants.REFERENCE_ORIGINAL_IMAGE, referenceInfo.getImageUrl());
        // 添加风格lora配置
        if (Objects.nonNull(referenceInfo.getReferenceConfig())) {
            // 设置 lens
            target.addExtInfo(CommonConstants.KEY_LENS,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_LENS));
            // 设置 posture
            target.addExtInfo(CommonConstants.KEY_POSTURE,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_POSTURE));
            // 设置 style
            target.addExtInfo(CommonConstants.KEY_STYLE,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_STYLE));
        }
        // 设置扩展标签
        target.addExtInfo(CommonConstants.BACK_TAGS, referenceInfo.getBackExtTags());
        // 设置 lora 地址
        target.addExtInfo(CommonConstants.KEY_LORA_PATH, referenceInfo.getLoraPath());

        // 设置图片比例
        target.setImageProportion(batch.getImageProportion());

        target.setBatchCnt(batch.getIntegerFromExtInfo(IMAGE_COUNT));

        // 设置为 ‘示例图创作’ 用户
        batch.setUserId(CommonUtil.mockPostureSampleDiagramContext().getMasterUser());

        // 设置姿势场景 id
        target.addExtInfo(CommonConstants.KEY_POSE_ELEMENT_ID, batch.getIntegerFromExtInfo(CURRENT_POSE_ID));

        // 添加姿势示例图关键字
        target.addExtInfo(CommonConstants.KEY_BIZTAG, CommonConstants.POSE_SAMPLE_DIAGRAM);

        // 设置视角参数
        target.addExtInfo(KEY_CAMERA_ANGLE, extractCameraAngles(referenceInfo));

        // 添加需要所有服装描述标识
        target.addExtInfo(IS_NEED_ALL_SCENE_OUTFIT, YES);

        // 插入任务信息
        CreativeTaskVO data = creativeTaskService.insert(target);

        // 添加至集合中
        result.add(data);

        log.info(
                "[ComfyUI流程][姿势示例图]PoseSampleDiagramCreativeService::buildTasks::CreativeTaskVO 任务数据构建完成:{}",
                result);

        return result;
    }

    @Override
    protected Map<Integer, List<Integer>> getConfigs(AddCreativeRequest request) {
        return request.getPoseSampleDiagram().getConfigs();
    }

    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task, Map<String, Object> context) {
        // 调用祖父类方法，传递姿势示例图的日志前缀
        String flowKey = determineFlowKeyWithLogging(elements, modelVO, task, context, "【姿势示例图创作】");

        // 将通用流程映射为固定姿势创作专用流程
        return mapToFixedPostureFlow(flowKey);
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context) {
        // 调用父级方法
        super.preParse(task, elements, modelVO, context);

        // 获取服装提示词
        String pictureMattingPrompt = task.getExtInfo(PICTURE_MATTING_PROMPT);
        if (StringUtils.isNoneBlank(pictureMattingPrompt)) {
            modelVO.setTags(modelVO.getTags() + pictureMattingPrompt);
        }

        // 将参考图设置进入上下文
        context.put(REFERENCE_IMAGE, task.getStringFromExtInfo(REFERENCE_IMAGE));

        context.put("lora", modelVO);
    }

    /**
     * 模拟模型对象
     */
    private MaterialModelVO mockModelVO() {
        // 初始化服装模型信息
        MaterialModelVO mockModelVO = new MaterialModelVO();
        // 设置为Flux场景
        mockModelVO.setVersion(ModelVersionEnum.FLUX);
        // 设置状态
        mockModelVO.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
        // 设置模型类型
        mockModelVO.setType(ModelTypeEnum.CUSTOM);

        // 返回服装信息
        return mockModelVO;
    }

    /**
     * 重新检查服装信息
     *
     * @param modelVO 服装数据
     * @return 服装信息
     */
    protected MaterialModelVO reCheckModelVO(MaterialModelVO modelVO) {
        return modelVO == null ? mockModelVO() : modelVO;
    }

    /**
     * 数据修正
     *
     * @param elements   元素列表
     * @param modelVO    模型数据
     * @param context    配置map上下文
     * @param task       任务
     * @param promptSeed prompt的种子
     */
    protected void dataCorrect(List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context,
                               CreativeTaskVO task, long promptSeed) {
        context.put(KEY_IS_FIXED_POSE, true);

        Integer loraId = task.getIntegerFromExtInfo(KEY_MODEL_ID);
        if (loraId != null) {
            CreativeElementVO sceneElement = creativeElementService.selectById(loraId);
            if (sceneElement != null) {
                // 添加场景元素
                elements.add(sceneElement);

                // 服装描述信息
                String styleOutfit = sceneElement.getExtInfo(KEY_STYLE_OUTFIT, String.class);
                // 服装类型
                String clothType = task.getStringFromExtInfo(KEY_CLOTH_TYPE);

                // 填充模型信息
                fillMockModel(modelVO, clothType, styleOutfit);
                context.put(ElementConfigKeyEnum.SCENE.name(), sceneElement);
            }
        }

        // 重新构建模特信息
        buildFaceVoByFace(elements, context, task);
    }


    /**
     * 填充模拟model
     *
     * @param modelVO     模拟服装对象
     * @param clothType   服装类型
     * @param styleOutfit 服装描述
     */
    private void fillMockModel(MaterialModelVO modelVO, String clothType, String styleOutfit) {
        // 1、初始化训练参数
        LoraTrainDetail loraTrainDetail = new LoraTrainDetail();
        loraTrainDetail.setClothType(clothType);
        loraTrainDetail.setClothDir(StringUtils.EMPTY);

        // 2、初始化服装类型配置参数
        ClothTypeConfig clothTypeConfig = new ClothTypeConfig();

        // 使用ArrayList预设容量提升性能
        List<String> types = getStrings(clothType);
        clothTypeConfig.setType(types);

        // 3、初始化颜色参数（使用单一对象避免创建集合）
        ClothColorDetail clothColorDetail = new ClothColorDetail();
        clothColorDetail.setIndex(1);
        clothColorDetail.setValue(styleOutfit);
        clothColorDetail.setViews(styleOutfit);

        // 直接使用Collections.singletonList减少内存分配
        clothTypeConfig.setColorList(Collections.singletonList(clothColorDetail));
        // 设置模型配置信息
        modelVO.setClothTypeConfigs(Collections.singletonList(clothTypeConfig));
        // 设置训练参数
        modelVO.setClothLoraTrainDetail(loraTrainDetail);

        // 从Spring配置文件中获取当前激活的环境
        String loraName;
        if (EnvUtil.isLocalEnv()) {
            loraName = "local/白色套装训练计划_100606_20250214_114825/白色套装训练计划_100606_20250214_114825-flux"
                    + "/白色套装训练计划_100606_20250214_114825-flux.safetensors";
        } else if (EnvUtil.isDevEnv()) {
            loraName = "dev/男款上衣_copy_2_100627_20250422_191515/男款上衣_copy_2_100627_20250422_191515-flux/男款上衣_copy_2_100627_20250422_191515-flux.safetensors";
        } else {
            loraName = "product/6308_8607_20250311_133310/6308_8607_20250311_133310-flux/6308_8607_20250311_133310-flux"
                    + ".safetensors";
        }
        modelVO.setLoraName(loraName);
    }

    @NotNull
    private static List<String> getStrings(String clothType) {
        List<String> types = new ArrayList<>(3);
        // 设置为v2版本
        types.add(ModelVersionEnum.FLUX.getCode());
        // 默认为正面图
        types.add(CameraAngleEnum.FRONT_VIEW.getCode());

        // 使用contains优化判断逻辑
        if (clothType != null) {
            if (clothType.contains("upper")) {
                types.add(CameraAngleEnum.UPPER_BODY.getCode());
            } else if (clothType.contains("lower")) {
                types.add(CameraAngleEnum.LOWER_BODY.getCode());
            }
        }
        return types;
    }

    /**
     * 重新构建模特信息
     *
     * @param elements 模特场景信息
     * @param context  上下文内容
     * @param task     任务信息
     */
    private void buildFaceVoByFace(List<CreativeElementVO> elements, Map<String, Object> context, CreativeTaskVO task) {
        CreativeElementVO originFaceElement = elements.stream()
                .filter(element -> element.getConfigKey().equals(ElementConfigKeyEnum.FACE.name())
                        && element.getLevel() == 3)
                .findFirst()
                .orElse(null);

        CreativeElementVO faceElement = CommonUtil.deepCopy(originFaceElement);

        // 如果模特为空则进行初始化
        if (faceElement == null) {
            // 1、初始化模特信息
            faceElement = new CreativeElementVO();

            //noinspection unchecked
            List<String> cameraAngle = task.getExtInfo(KEY_CAMERA_ANGLE, List.class);
            // 额外添加一个背部视角
            cameraAngle.add(CameraAngleEnum.BACK_VIEW.getCode());
            task.addExtInfo(KEY_CAMERA_ANGLE, cameraAngle);

            faceElement.setParentId(1);
            faceElement.setLevel(3);
            faceElement.setConfigKey(ElementConfigKeyEnum.FACE.name());
            faceElement.setStatus(ElementStatusEnum.PROD);
            // 初始化扩展信息
            faceElement.setExtInfo(new JSONObject());

            faceElement.addExtInfo(KEY_REPAIR_FACE_TYPE, NO);

            // 其他脸部图片置为空
            faceElement.addExtInfo(KEY_FACE_IMAGE_MORE, null);
            // face lora 强度置为 0
            faceElement.addExtInfo(KEY_FACE_LORA_STRENGTH, 0);
            // extTags 置为空
            faceElement.setExtTags(StringUtils.EMPTY);
            // tags 置为空
            faceElement.setTags(StringUtils.EMPTY);
            // expression 置为空
            faceElement.addExtInfo(KEY_EXPRESSION, StringUtils.EMPTY);
            // hairstyle 置为空
            faceElement.addExtInfo(KEY_HAIRSTYLE, StringUtils.EMPTY);
            // negative 置为空
            faceElement.addExtInfo(KEY_NEGATIVE, StringUtils.EMPTY);
            // 设置faceRestoreVisibility
            faceElement.addExtInfo(KEY_FACE_RESTORE_VISIBILITY, "0.1");

            // 添加进入集合中
            elements.add(faceElement);
        }

        // 添加模特信息
        context.put(ElementConfigKeyEnum.FACE.name(), faceElement);
    }



    /**
     * 提取视角参数
     *
     * @param referenceInfo 参考图信息
     * @return 视角参数列表
     */
    private List<Object> extractCameraAngles(AddCreativeRequest.ReferenceInfo referenceInfo) {
        if (referenceInfo.getReferenceConfig() == null) {
            return Collections.singletonList(CameraAngleEnum.FRONT_VIEW.getCode());
        }

        try {
            Object lensValue = referenceInfo.getReferenceConfig().get(KEY_LENS);
            if (lensValue instanceof JSONArray) {
                return ((JSONArray) lensValue).toJavaList(Object.class);
            } else if (lensValue != null) {
                return Collections.singletonList(lensValue);
            }
        } catch (Exception e) {
            log.warn("解析视角参数失败，使用默认值", e);
        }

        return Collections.singletonList(CameraAngleEnum.FRONT_VIEW.getCode());
    }


}
