/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_LABEL_CLOTH_DETAILS_PROMPT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_LABEL_CLOTH_MINI_PROMPT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_LABEL_CLOTH_PROMPT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_LABEL_FACE_MINI_PROMPT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TRAIN_LABEL_FACE_PROMPT;

/**
 * 打标类型枚举
 *
 * <AUTHOR>
 * @version : LabelTypeEnum.java, v 0.1 2025/2/22 10:19 renxiao.wu Exp $
 */
@Getter
public enum LabelTypeEnum {
    DEFAULT("default", "默认打标", TRAIN_LABEL_CLOTH_PROMPT, TRAIN_LABEL_FACE_PROMPT),
    DETAILS("details", "精准打标", TRAIN_LABEL_CLOTH_DETAILS_PROMPT, null),
    MINI("mini", "极简打标", TRAIN_LABEL_CLOTH_MINI_PROMPT, TRAIN_LABEL_FACE_MINI_PROMPT),
    STRUCTURAL("structural", "结构化打标", null, null),
    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    /** 配置的关键字 */
    private final String promptKey;

    /** 模特配置的关键字 */
    private final String facePromptKey;

    private LabelTypeEnum(String code, String desc, String promptKey, String facePromptKey) {
        this.code = code;
        this.desc = desc;
        this.promptKey = promptKey;
        this.facePromptKey = facePromptKey;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static LabelTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (LabelTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}
