package ai.conrain.aigc.platform.service.enums;

/**
 * 搜索任务状态枚举
 *
 * <AUTHOR>
 * @version SearchTaskStatusEnum.java v 0.1 2025-08-19
 */
public enum SearchTaskStatusEnum {

    /**
     * 初始化
     */
    INIT("INIT", "初始化"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成");

    /** 状态码 */
    private final String code;

    /** 状态描述 */
    private final String desc;

    SearchTaskStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     */
    public static SearchTaskStatusEnum getByCode(String code) {
        for (SearchTaskStatusEnum status : SearchTaskStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}