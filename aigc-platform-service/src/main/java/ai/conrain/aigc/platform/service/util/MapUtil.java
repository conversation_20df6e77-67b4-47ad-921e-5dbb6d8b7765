package ai.conrain.aigc.platform.service.util;

import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * Map工具类，提供扁平化和嵌套化功能
 */
public class MapUtil {
    
    /**
     * 默认分隔符
     */
    private static final String DEFAULT_SEPARATOR = ".";

    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }

    /**
     * 将嵌套的Map扁平化为点分格式的键值对
     * 使用默认分隔符 "."
     * 
     * @param nestedMap 嵌套的Map
     * @return 扁平化后的Map
     */
    public static Map<String, Object> flatten(Map<String, Object> nestedMap) {
        return flatten(nestedMap, DEFAULT_SEPARATOR);
    }
    
    /**
     * 将嵌套的Map扁平化为指定分隔符格式的键值对
     * 
     * @param nestedMap 嵌套的Map
     * @param separator 分隔符
     * @return 扁平化后的Map
     */
    public static Map<String, Object> flatten(Map<String, Object> nestedMap, String separator) {
        Map<String, Object> flatMap = new HashMap<>();
        flattenRecursive(nestedMap, "", separator, flatMap);
        return flatMap;
    }
    
    /**
     * 递归扁平化Map
     * 
     * @param map 当前处理的Map
     * @param prefix 键前缀
     * @param separator 分隔符
     * @param result 结果Map
     */
    @SuppressWarnings("unchecked")
    private static void flattenRecursive(Map<String, Object> map, String prefix, String separator, Map<String, Object> result) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            String newKey = prefix.isEmpty() ? key : prefix + separator + key;
            
            if (value instanceof Map) {
                // 如果值是Map，递归处理
                flattenRecursive((Map<String, Object>) value, newKey, separator, result);
            } else {
                // 如果值不是Map，直接添加到结果中
                result.put(newKey, value);
            }
        }
    }
    
    public static JSONObject rebuildNestedJson(String s) {
        JSONObject jsonObject = JSONObject.parseObject(s);

        // 创建analysis对象并添加到result中
        JSONObject analysisObj = new JSONObject();

        if (jsonObject != null) {
            for (String key : jsonObject.keySet()) {
                Object value = jsonObject.get(key);
                addToNestedObject(analysisObj, key, value);
            }
        }

        return analysisObj;
    }

    /**
     * 将扁平的键（如"Model.Age"）转换为嵌套的JSON对象结构
     *
     * @param parent  父级JSONObject
     * @param flatKey 扁平的键，如"Model.Age"
     * @param value   值
     */
    private static void addToNestedObject(JSONObject parent, String flatKey, Object value) {
        String[] parts = flatKey.split("\\.");

        // 如果只有一部分，直接添加到parent
        if (parts.length == 1) {
            parent.put(parts[0], value);
            return;
        }

        // 处理嵌套结构
        JSONObject current = parent;
        for (int i = 0; i < parts.length - 1; i++) {
            String part = parts[i];
            if (!current.containsKey(part)) {
                current.put(part, new JSONObject());
            }

            Object next = current.get(part);
            if (next instanceof JSONObject) {
                current = (JSONObject) next;
            } else {
                // 如果该键已存在但不是JSONObject，我们不能继续嵌套，所以直接添加到最后一个part
                // 这种情况理论上不应该发生，但为了安全起见加上处理
                break;
            }
        }

        // 添加最终的键值对
        current.put(parts[parts.length - 1], value);
    }
}
