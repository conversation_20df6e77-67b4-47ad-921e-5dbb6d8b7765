package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.example.ImageCaptionExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.model.converter.ImageCaptionConverter;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class FixSortEmbeddingJob extends JavaProcessor {

    @Autowired
    private ImageCaptionDAO imageCaptionDAO;

    @Autowired
    private ImageCaptionService imageCaptionService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        try {
            ImageCaptionExample exam = new ImageCaptionExample();
            exam.createCriteria().andImgEmbIsNull().andCaptionIsNotNull();
            exam.setRows(20);
            exam.setOrderByClause("id asc");

            List<ImageCaptionDO> imageCaptionDOS = imageCaptionDAO.selectSimpleByExample(exam);
            if (CollectionUtils.isEmpty(imageCaptionDOS)) {
                return new ProcessResult(true);
            }

            // 使用for循环顺序处理
            for (ImageCaptionDO each : imageCaptionDOS) {
                String currentTraceId = uuid + "_" + each.getId();
                MDC.put("traceId", currentTraceId);
                try {
                    imageCaptionService.initSortEmbeddings(ImageCaptionConverter.do2VO(each));
                } catch (Exception e) {
                    log.error("[SceneImageCaptionJob] error processing imageCaption: {}", each.getId(), e);
                } finally {
                    MDC.remove("traceId");
                }
            }

        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }
}
