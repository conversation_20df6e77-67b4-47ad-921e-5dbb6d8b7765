package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.example.ImageCaptionExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.onnx.GenreRecognizeOnnxService;
import ai.conrain.aigc.platform.service.component.onnx.ImageQualityOnnxService;
import ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class FixCaptionGenreJob extends JavaProcessor {

    @Autowired
    private ImageCaptionDAO imageCaptionDAO;

    @Autowired
    private ImageService imageService;

    @Autowired
    private ImageCaptionService imageCaptionService;

    @Autowired
    private ImageQualityOnnxService imageQualityOnnxService;

    @Autowired
    private GenreRecognizeOnnxService genreRecognizeOnnxService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        try {
            ImageCaptionExample exam = new ImageCaptionExample();
            exam.createCriteria().andImageTypeEqualTo("scene").andCaptionIsNotNull().andGenreIsNull();
            exam.setRows(50);
            exam.setOrderByClause("id asc");

            List<ImageCaptionDO> imageCaptionVOS = imageCaptionDAO.selectByExample(exam);

            for (ImageCaptionDO imageCaptionVO : imageCaptionVOS) {
                if (imageCaptionVO.getCaption() == null || !CommonUtil.isValidJson(imageCaptionVO.getCaption())) {
                    continue;
                }
                ImageVO imageVO = imageService.selectById(imageCaptionVO.getImageId());
                if (imageVO != null) {
                    try {
                        String recognizeGenre = genreRecognizeOnnxService.recognizeGenreFromUrl(imageVO.getUrl());
                        ClothShootGenreEnum genre = ClothShootGenreEnum.getByCode(recognizeGenre);
                        if (genre != null) {
                            ImageCaptionVO target = new ImageCaptionVO();
                            target.setId(imageCaptionVO.getId());
                            target.setGenre(genre.getCode());

                            imageCaptionService.updateByIdSelective(target);
                        }
                    } catch (Exception e) {
                        log.error("[ImageGalleryController] fixImageCaptionGenre error for imageId:{}", imageCaptionVO.getImageId(), e);
                    }
                }
            }
        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }
}
