package ai.conrain.aigc.platform.service.util;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialImg;
import ai.conrain.aigc.platform.service.model.biz.CommonMaterialDetail;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialInfoVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class MaterialUploadUtil {

    //子目录按环境隔开，AIGC_USER_UPLOAD：生产，AIGC_USER_UPLOAD_DEV：开发，AIGC_USER_UPLOAD_LOCAL：本地
    public static String getMaterialUploadBaseDir() {

        String envDir = "AIGC_USER_UPLOAD";
        if (EnvUtil.isLocalEnv() || EnvUtil.isDevEnv()) {
            envDir = "AIGC_USER_UPLOAD_" + EnvUtil.getEnv().toUpperCase();
        }

        return envDir;
    }

    //lora文件存放目录，按环境区分
    public static String getLoraBaseDir() {
        //当前生产目录，兼容
        String envDir = "/home/<USER>/aigc/ComfyUI/models/loras/product/";
        if (EnvUtil.isLocalEnv() || EnvUtil.isDevEnv()) {
            envDir = "/home/<USER>/aigc/ComfyUI/models/loras/" + EnvUtil.getEnv().toLowerCase() + "/";
        }
        return envDir;
    }

    //根据view tags过滤出上半身图
    public static List<ClothMaterialImg> getUpperDetailImgs(List<ClothMaterialImg> imgList) {
        List<ClothMaterialImg> list = new ArrayList<>();
        if (imgList != null) {
            for (ClothMaterialImg img : imgList) {
                if (img.getViewTags().contains("upper")) {
                    list.add(img);
                }
            }
        }
        return list;
    }

    //根据view tags过滤出下半身图
    public static List<ClothMaterialImg> getLowerDetailImgs(List<ClothMaterialImg> imgList) {
        List<ClothMaterialImg> list = new ArrayList<>();
        if (imgList != null) {
            for (ClothMaterialImg img : imgList) {
                if (img.getViewTags().contains("lower")) {
                    list.add(img);
                }
            }
        }
        return list;
    }

    public static String getShowImage4Model(JSONObject materialDetail) {
        return getShowImage4Model(JSONObject.parseObject(materialDetail.toJSONString(), ClothMaterialDetail.class));
    }

    public static String getShowImage4Model(ClothMaterialDetail clothMaterialDetail) {
        String imgUrl = clothMaterialDetail.getFullShotImgList().get(0).getImgUrl();
        for (ClothMaterialImg img : clothMaterialDetail.getFullShotImgList()) {
            if (img.getViewTags() != null && img.getViewTags().contains("full_front") && StringUtils.isNotBlank(
                img.getImgUrl())) {

                imgUrl = img.getImgUrl();
                break;
            }
        }

        return imgUrl;
    }

    public static List<ClothMaterialImg> getClothMaterialImgs4Upload(ClothMaterialDetail materialDetail,
                                                                     String clothType) {
        List<ClothMaterialImg> imgs = new ArrayList<>(materialDetail.getFullShotImgList());

        //上传图片，连衣裙和上下装，不传入下半身图片到lora抠图和训练
        if (StringUtils.equalsIgnoreCase(clothType, ClothTypeEnum.OnePiece.getCode()) || StringUtils.equalsIgnoreCase(
            clothType, ClothTypeEnum.TwoPiece.getCode())) {
            imgs.addAll(MaterialUploadUtil.getUpperDetailImgs(materialDetail.getDetailShotImgList()));
        } else {
            imgs.addAll(materialDetail.getDetailShotImgList());
        }

        if (materialDetail.getMoreImgList() != null) {
            imgs.addAll(materialDetail.getMoreImgList());
        }
        return imgs;
    }

    public static int getExpectedImgNum(MaterialInfoVO materialInfoVO) {

        JSONObject materialDetail = materialInfoVO.getMaterialDetail();

        //face/scene
        if (MaterialType.face.name().equals(materialInfoVO.getType()) || MaterialType.scene.name().equals(
            materialInfoVO.getType())) {
            CommonMaterialDetail detail = materialDetail.toJavaObject(CommonMaterialDetail.class);
            return detail.getImgUrls().size();

        } else {
            ClothMaterialDetail detail = materialDetail.toJavaObject(ClothMaterialDetail.class);

            List<ClothMaterialImg> toBeUploadDetails = detail.getDetailShotImgList();

            //除「仅下装」外，都只取上半身图，不要下半身图
            if (!StringUtils.equalsIgnoreCase(materialInfoVO.getSubType(), ClothTypeEnum.Bottoms.getCode())) {
                toBeUploadDetails = getUpperDetailImgs(detail.getDetailShotImgList());
            }

            int expectedImgNum = detail.getFullShotImgList().size() + CollectionUtils.size(toBeUploadDetails);
            if (detail.getMoreImgList() != null) {
                expectedImgNum += detail.getMoreImgList().size();
            }
            return expectedImgNum;
        }
    }

    public static String getClothType4TrainDetail(String clothType) {
        if (StringUtils.equals(clothType, ClothTypeEnum.Tops.getCode())) {
            return CommonConstants.upperGarment;
        } else if (StringUtils.equals(clothType, ClothTypeEnum.Bottoms.getCode())) {
            return CommonConstants.lowerGarment;
        } else {
            return CommonConstants.outfit;
        }
    }

    public static String[] getLabelFileTypes(ComfyuiTaskVO task) {
        boolean isScene = StringUtils.equals(task.getReqParams().getString("materialType"), "scene");
        return getLabelFileTypes(isScene);
    }

    public static String[] getLabelFileTypes(MaterialModelVO model) {
        boolean isScene = model != null && StringUtils.equals(model.getClothLoraTrainDetail().getMaterialType(),
            MaterialType.scene.name());
        return getLabelFileTypes(isScene);
    }

    private static String[] getLabelFileTypes(boolean isScene) {
        return isScene ? new String[] {"img", "json"} : new String[] {"img", "text", "json"};
        //return new String[] {"img", "text", "json"};
    }
}
