package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.StatsQueuedCreativeDO;
import ai.conrain.aigc.platform.dal.entity.StatsUserQueuedCreativeDO;
import ai.conrain.aigc.platform.service.enums.CreativeBizTypeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.TryonTaskParams;
import ai.conrain.aigc.platform.service.model.biz.VideoClipGenReq;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.UserCountVO;
import ai.conrain.aigc.platform.service.model.query.CommonTaskQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.request.CreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.LoraOption;
import java.io.IOException;
import java.util.List;

/**
 * 创作批次 Service定义
 *
 * <AUTHOR>
 * @version CreativeBatchService.java v 0.1 2024-05-08 03:35:56
 */
public interface CreativeBatchService {

    /**
     * 查询创作批次对象
     *
     * @param id 主键
     * @return 返回结果
     */
    CreativeBatchVO selectById(Integer id);

    /**
     * 根据id查询创作批次以及子任务
     *
     * @param id 主键
     * @return 返回结果
     */
    CreativeBatchVO getCreativeBatchByIdWithTask(Integer id);

    /**
     * 查询创作批次对象，如果状态非结束，则进行一次状态更新
     *
     * @param id 主键
     * @return 返回结果
     */
    CreativeBatchVO getAndSync(Integer id);

    /**
     * 批量查询创作批次对象
     *
     * @param ids      id列表
     * @param isSelect 是否查询
     * @return 结果
     */
    List<CreativeBatchVO> batchQueryByIds(List<Integer> ids, Boolean isSelect);

    /**
     * 删除创作批次对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 执行创作服务
     *
     * @param type    类型
     * @param request 请求
     * @param <T>     请求类型
     * @return 结果
     * @throws IOException 异常
     */
    <T extends CreativeRequest> CreativeBatchVO create(CreativeTypeEnum type, T request) throws IOException;

    //替换视频创作中的一张图
    void updateOriginalImg4Video(Integer batchId, Integer index, String imageUrl);

    /**
     * 修改创作批次对象
     *
     * @param creativeBatch 对象参数
     */
    void updateByIdSelective(CreativeBatchVO creativeBatch);

    /**
     * 带条件批量查询创作批次列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<CreativeBatchVO> queryCreativeBatchList(CreativeBatchQuery query);

    /**
     * 带条件分页查询创作批次
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<CreativeBatchVO> queryCreativeBatchByPage(CreativeBatchQuery query);

    /**
     * 同步创作状态
     *
     * @param data 创作记录
     */
    void syncStatus(CreativeBatchVO data);

    /**
     * 查询当前用户进行中的创作批次
     *
     * @param types     创作类型列表
     * @param modelType 模型类型
     * @return 进行中的创作批次
     */
    List<CreativeBatchVO> queryActive(List<String> types, ModelTypeEnum modelType);

    /**
     * 查询当前用户今日的创作批次
     *
     * @param types     创作类型列表
     * @param modelType 模型类型
     * @param isSelect  是否选择
     * @return 今日的创作批次
     */
    List<CreativeBatchVO> queryTodayList(List<String> types, ModelTypeEnum modelType, boolean isSelect,
                                         CreativeBizTypeEnum bizType);

    /**
     * 取消创作
     *
     * @param id id
     */
    void cancel(Integer id);

    /**
     * 清除去创作页面的创作记录
     */
    void clear(List<String> types, ModelTypeEnum modelType);

    List<LoraOption> queryModels4HistoryTasks();

    /**
     * 图片点赞
     *
     * @param taskId   任务id
     * @param batchId  批次id
     * @param like     是否喜欢
     * @param imageUrl 图片url
     */
    void imageLike(Integer taskId, Integer batchId, Boolean like, String imageUrl);

    /**
     * 基于用户维度查询未完成的批次
     *
     * @param pipelineId 管道id
     * @param limit      限制数量
     * @return 批次结果
     */
    List<CreativeBatchVO> queryUnCompletedTopUser(Integer pipelineId, Integer limit);

    /**
     * 基于用户维度查询未处理的批次
     *
     * @param pipelineId 管道id
     * @param limit      限制数量
     * @return 批次结果
     */
    List<CreativeBatchVO> queryUnProcessedTopUser(Integer pipelineId, Integer limit);

    /**
     * 基于用户维度查询未处理的批次
     *
     * @param pipelineId      管道id
     * @param exceptTypeList  排除类型列表
     * @param includeTypeList 包含类型列表
     * @param limit           限制数量
     * @return 批次结果
     */
    List<CreativeBatchVO> queryUnProcessedTopUser(Integer pipelineId, List<String> exceptTypeList,
                                                  List<String> includeTypeList, Integer limit);

    /**
     * 基于用户维度查询未完成的批次
     *
     * @param pipelineId     管道id
     * @param exceptTypeList 排除类型列表
     * @param limit          限制数量
     * @return 批次结果
     */
    List<CreativeBatchVO> queryUnCompletedTopUser(Integer pipelineId, List<String> exceptTypeList,
                                                  List<String> includeTypeList, Integer limit);

    /**
     * 插入批次数据
     *
     * @param data 批次数据
     * @return 结果
     */
    CreativeBatchVO insert(CreativeBatchVO data);

    /**
     * 变更示例图列表
     *
     * @param modelId       模型id
     * @param userId        用户id
     * @param exampleImages 示例图列表
     */
    void changeExampleImages(Integer modelId, Integer userId, List<String> exampleImages);

    /**
     * 清空示例图
     *
     * @param modelId 模型id
     * @param userId  用户id
     */
    void clearExampleImages(Integer modelId, Integer userId);

    /**
     * 根据模型id查询示例图
     *
     * @param modelId 模型id
     * @return 示例图
     */
    List<String> queryExampleImages(Integer modelId);

    /**
     * 转移示例图
     *
     * @param modelId 模型id
     * @param userId  用户id
     */
    void assignExampleImages(Integer modelId, Integer userId);

    /**
     * 设置为失败
     *
     * @param id id
     */
    void setToFail(Integer id);

    /**
     * 上传视频
     *
     * @param id     id
     * @param videos 视频oss地址列表
     */
    void uploadVideo(Integer id, List<String> videos);

    /**
     * 删除修脸视频
     *
     * @param id    id
     * @param index index
     */
    void removeFixFace(Integer id, Integer index);

    /**
     * 指定视频创作跟进人
     *
     * @param id     视频创作id
     * @param mobile 跟进人手机号
     */
    void assignVideoOperator(Integer id, String mobile);

    /**
     * 根据条件查询数量
     *
     * @param query 查询条件
     * @return 记录条数
     */
    long queryCount(CreativeBatchQuery query);

    /**
     * 提交精修任务
     *
     * @param id 批次id
     */
    void applyRefine(Integer id);

    /**
     * 完成精修任务
     *
     * @param id 批次id
     */
    void completeRefine(Integer id);

    /**
     * 变更视频临时文件
     *
     * @param id       批次id
     * @param index    索引
     * @param videoUrl 视频地址
     */
    void changeTempVideo(Integer id, Integer index, String videoUrl);

    /**
     * 申请创建视频片段
     *
     * @param item
     */
    void apply2GenVideoClip(VideoClipGenReq item);

    /**
     * 重置为处理中
     *
     * @param id id
     */
    void resetProcessing(Integer id);

    /**
     * 下载全部图片
     *
     * @param id        批次id
     * @param imageUrls 需要下载的图片链接
     * @return zipurl
     */
    String downloadAll(Integer id, List<String> imageUrls) throws IOException;

    /**
     * 转交批次任务给指定用户
     *
     * @param batchId 批次id
     * @param userId  用户id
     */
    void assignTo(Integer batchId, Integer userId);

    /**
     * 查询所有未完成的外部调用任务
     *
     * @return list
     */
    List<CreativeBatchVO> queryUnCompletedExternal();

    /**
     * 根据元素id查询图片列表
     *
     * @param elementId 元素id
     * @param userId    用户id
     * @param testFlag  是否测试的标识
     * @param limit     限制数量
     */
    List<String> queryImagesByElement(Integer elementId, Integer userId, Boolean testFlag, Integer limit);

    /**
     * 根据元素id分页查询图片列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageInfo<String> queryImagesByElementWithPage(CreativeBatchQuery query);

    Integer createAliyunTryonTask(TryonTaskParams params);

    Integer createTryonRefinerTask(Integer tryonTaskId, String gender);

    CommonTaskVO queryAliyunTryonTask(Integer taskId);

    PageInfo<CommonTaskVO> queryAliyunTryonTasksByPage(CommonTaskQuery query);

    /**
     * 添加演示标记，如果已经有标记，则取消
     *
     * @param id id
     */
    void addDemoTag(Integer id);

    /**
     * 根据模型id查询已创作的图片张数
     *
     * @param modelId 模型id
     * @return 图片张数
     */
    int queryCreateImageCntByModelId(Integer modelId);

    /**
     * 根据模型id查询已完成创作的系统创建图片张数
     *
     * @param modelId 模型id
     * @return 图片张数
     */
    int queryFinishedSysGenImageCntByModelId(Integer modelId);

    /**
     * 统计队列中的用户数据
     *
     * @return list
     */
    List<UserCountVO> statsQueuedUser();

    /**
     * 统计队列中的数据
     *
     * @return 结果
     */
    StatsQueuedCreativeDO statsQueuedCreative();

    /**
     * 统计用户创作等待队列数据
     *
     * @return 用户创作等待队列数据
     */
    List<StatsUserQueuedCreativeDO> statsCustomerQueuedCreative();

    /**
     * 统计后台用户创作等待队列数据
     *
     * @return 用户创作等待队列数据
     */
    List<StatsUserQueuedCreativeDO> statsBackUserQueuedCreative();

    /**
     * 根据姿势ID列表批量查询每个ID的最新创作记录
     *
     * @param poseIdList 姿势ID列表
     * @return 最新的创作记录列表
     */
    List<CreativeBatchVO> getLatestCreativeBatchByPoseIds(List<Integer> poseIdList);
}