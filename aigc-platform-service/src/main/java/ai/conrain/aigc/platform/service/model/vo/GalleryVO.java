package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.GallerySubTypeEnum;
import ai.conrain.aigc.platform.service.enums.GalleryTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import com.alibaba.fastjson.JSONObject;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * GalleryVO
 *
 * @version GalleryService.java v 0.1 2025-08-20 02:49:20
 */
@Data
public class GalleryVO implements IExtModel, Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	private Integer id;

	/** 用户id */
	private Integer userId;

	/** 操作人id */
	private Integer operatorId;

	/** 一级分类(按功能分) */
	private GalleryTypeEnum type;

	/** 二级分类(功能内细分) */
	private GallerySubTypeEnum subType;

	/** 单张图片url */
	private String imageUrl;

	/** md5值 */
	private String md5;

	/** 归属 */
	private ModelTypeEnum belong;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

	/** 配置信息 */
	private JSONObject extInfo;

	/** 标签信息, 用于更新标签 */
	private List<String> tags;

}