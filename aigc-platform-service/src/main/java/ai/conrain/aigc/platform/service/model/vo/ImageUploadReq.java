package ai.conrain.aigc.platform.service.model.vo;

import com.alibaba.fastjson2.JSONObject;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import lombok.Data;

/**
 * ImageUploadReq
 *
 * @version ImageService.java v 0.1 2025-07-30 02:33:24
 */
@Data
public class ImageUploadReq implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 图像类型，用于区分服装图、风格示意图 */
    @NotBlank
	private String type;

    /** 元数据 */
    private JSONObject metadata;

	/** 图片base64 */
	private String imageBase64;

    /** 图片路径 */
    @NotBlank
    private String imagePath;

	/** 图像元数据，JSON格式 */
	private JSONObject data;
}
