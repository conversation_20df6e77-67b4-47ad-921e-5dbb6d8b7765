package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.List;

@Slf4j
@Component
public class SceneImageCaptionJob extends JavaProcessor {

    @Autowired
    private ImageService imageService;

    @Autowired
    private ImageCaptionService imageCaptionService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        Integer count = systemConfigService.queryIntValue(SystemConstants.CAPTION_COUNT, 20);

        try {
            List<ImageVO> imageVOS = imageService.selectGeminiFlashCaptionedSceneImages4Embedding(count);
            if (CollectionUtils.isEmpty(imageVOS)) {
                log.info("[SceneImageCaptionJob] no uncaptioned scene images");
                return new ProcessResult(true);
            }

            // 使用for循环顺序处理
            for (ImageVO imageVO : imageVOS) {
                String currentTraceId = uuid + "_" + imageVO.getId();
                MDC.put("traceId", currentTraceId);
                try {
                    imageCaptionService.createImageCaptionEmbeddings(imageVO, true, true);
                } catch (Exception e) {
                    log.error("[SceneImageCaptionJob] error processing imageVO: {}", imageVO.getId(), e);
                } finally {
                    MDC.remove("traceId");
                }
            }

        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }
}
