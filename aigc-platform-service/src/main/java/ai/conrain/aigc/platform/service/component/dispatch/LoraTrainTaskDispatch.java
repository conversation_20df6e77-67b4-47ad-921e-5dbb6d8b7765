/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.dispatch;

import ai.conrain.aigc.platform.integration.ai.model.QueueResult.QueueCodeEnum;
import ai.conrain.aigc.platform.service.component.ComfyuiTaskService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.enums.ComfyuiTaskTypeEnum;
import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.biz.LoraSubTask;
import ai.conrain.aigc.platform.service.model.biz.LoraTaskParams;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.ComfyuiTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MODEL_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_URL;

/**
 * lora训练任务分发
 *
 * <AUTHOR>
 * @version : LoraTrainTaskDispatch.java, v 0.1 2024/8/26 17:25 renxiao.wu Exp $
 */
@Slf4j
@Service
public class LoraTrainTaskDispatch extends AbstractTaskDispatch {
    @Lazy
    @Autowired
    protected ComfyuiTaskService comfyuiTaskService;
    @Lazy
    @Autowired
    protected MaterialModelService materialModelService;
    @Autowired
    private FileDispatch fileDispatch;

    @Override
    protected List<? extends IExtModel> queryUnProcessedData(Integer pipelineId, int idleNum) {
        ComfyuiTaskQuery query = new ComfyuiTaskQuery();
        query.setTaskType(ComfyuiTaskTypeEnum.lora.getCode());
        query.setTaskStatus(QueueCodeEnum.QUEUED.name());
        query.setPageNum(1);
        query.setPageSize(idleNum);
        PageInfo<ComfyuiTaskVO> pageInfo = comfyuiTaskService.queryComfyuiTaskByPage(query);
        return pageInfo.getList();
    }

    @Override
    protected boolean isTaskFinished(Integer taskId) {
        if (taskId == null) {
            log.error("【任务分发】判断任务是否已经执行完毕，taskId为空，type={},taskId={}", getType(), taskId);
            return true;
        }

        ComfyuiTaskVO task = comfyuiTaskService.selectById(taskId);
        if (task == null) {
            log.info("【任务分发】判断任务是否已经执行完毕，task查询结果为空，type={},taskId={}", getType(), taskId);
            return true;
        }

        boolean completed = task.getTaskStatus() == QueueCodeEnum.COMPLETED;
        if (completed) {
            log.info("【任务分发】判断任务是否已经执行完毕，task任务已完结，type={},taskId={}", getType(), taskId);
            return true;
        }

        Integer modelId = task.getExtValue(KEY_MODEL_ID, Integer.class);
        if (modelId == null) {
            log.info("【任务分发】判断任务是否已经执行完毕，task任务未完结且modelId为空，type={},taskId={}", getType(),
                taskId);
            return completed;
        }

        MaterialModelVO model = materialModelService.selectById(modelId);
        if (model == null) {
            log.info("【任务分发】判断任务是否已经执行完毕，task任务未完结但模型数据未查询到，返回已完结，type={},taskId={}",
                getType(), taskId);
            return true;
        }

        completed = MaterialModelStatusEnum.getByCode(model.getStatus()) != MaterialModelStatusEnum.IN_TRAINING;

        if (completed) {
            log.info(
                "【任务分发】判断任务是否已经执行完毕，task任务未完结但模型数据状态已完结{}，返回已完结，type={},taskId={}",
                model.getStatus(), getType(), taskId);
            return true;
        }

        ComfyuiTaskTypeEnum taskType = task.getTaskType();
        LoraTrainDetail detail = model.getClothLoraTrainDetail();

        LoraSubTask subTask = null;
        switch (taskType) {
            case prepareView:
                subTask = detail.getPrepareView();
                break;
            case cutout:
                subTask = detail.getCutout();
                break;
            case label:
                subTask = detail.getLabel();
                break;
            case lora:
                subTask = detail.getLora();
                break;
            default:
                log.error("【任务分发】判断任务是否已经执行完毕，task任务未完结但未定义任务类型，type={},taskId={},type={}",
                    getType(), taskId, taskType);
                throw new BizException(ResultCode.SYS_ERROR);
        }

        if (subTask == null) {
            log.info(
                "【任务分发】判断任务是否已经执行完毕，task任务未完结但模型detail数据中已无对应任务可能进行了数据订正，返回已完结，type={},taskId={},modelId={}",
                getType(), taskId, modelId);
            return true;
        }

        if (!subTask.getTaskId().equals(taskId)) {
            log.info("【任务分发】判断任务是否已经执行完毕，task任务未完结但模型detail数据中对应任务id不一致，可能进行了数据订正，返回已完结，type={},taskId={},modelId={},"
                     + "currentTaskId={}", getType(), taskId, modelId, subTask.getTaskId());
            return true;
        }

        return false;
    }

    @Override
    protected PipelineVO fetchPipeline(Integer userId) {
        return serverHelper.fetchPipelineByUserAndType(userId, getType());
    }

    @Override
    protected boolean checkMachineMatch(DispatchTypeEnum type, IExtModel task, ServerVO server) {
        String serverUrl = fetchTaskServerUrl(task);
        ServerVO originServer = serverService.parseByUrl(serverUrl);
        if (originServer == null) {
            log.warn("【任务分发】任务要求服务器一致，但任务服务器{}解析失败，直接跳过，type={},taskId={}", serverUrl, type,
                task.getId());
            return true;
        }

        ServerVO originParent = serverService.queryByKey(originServer.getParentId());
        if (!serverHelper.isEnable(originParent)) {
            log.warn("【任务分发】任务要求服务器一致，但任务服务器{}状态为不可用，允许切换机器，type={},taskId={}",
                serverUrl, type, task.getId());
            return true;
        }

        String originMachine = originParent.getConfig();
        ServerVO parent = serverService.queryByKey(server.getParentId());
        String targetMachine = parent.getConfig();

        if (!StringUtils.equals(originMachine, targetMachine)) {

            //检查文件夹一致性
            if (canSwitchMachine(task) && checkFolderConsistency(type, task, server, originServer)) {
                return true;
            }

            log.info("【任务分发】任务要求服务器一致，当前服务器{}与任务服务器{}不一致，直接跳过，type={},taskId={}",
                targetMachine, originMachine, type, task.getId());
            return false;
        }

        String originIntranetIp = originParent.getIntranetAddress();
        String targetIntranetIp = parent.getIntranetAddress();

        if (StringUtils.isNotBlank(originIntranetIp) && StringUtils.isNotBlank(targetIntranetIp) && !StringUtils.equals(
            originIntranetIp, targetIntranetIp)) {

            //检查文件夹一致性
            if (canSwitchMachine(task) && checkFolderConsistency(type, task, server, originServer)) {
                return true;
            }

            log.info("【任务分发】任务要求服务器一致，当前服务器{}，内网ip不一致{}≠{}，直接跳过，type={},taskId={}",
                targetMachine, originIntranetIp, targetIntranetIp, type, task.getId());
            return false;
        }

        return true;
    }

    /**
     * 业务决策是否能执行
     *
     * @param task     任务
     * @param pipeline 管道
     * @param idleNum  空闲服务数
     * @return true，可以执行
     */
    @Override
    protected boolean canBizRun(IExtModel task, PipelineVO pipeline, int idleNum) {
        return true;
    }

    /**
     * 判断是否能切换机器
     *
     * @param task 任务
     * @return true，可以切换
     */
    protected boolean canSwitchMachine(IExtModel task) {
        ComfyuiTaskVO currTask = (ComfyuiTaskVO)task;
        if (currTask.getTaskStatus() != QueueCodeEnum.RUNNING) {
            log.info("【任务分发】任务要求服务器一致,当前训练任务未开始处理，允许切换机器,id={},status={}", task.getId(),
                currTask.getTaskStatus());
            return true;
        }

        String serverUrl = task.getStringFromExtInfo(KEY_SERVER_URL);
        if (!serverHelper.isEnableMachine(serverUrl)) {
            log.info(
                "【任务分发】任务要求服务器一致,当前训练任务正在处理中，但服务器已被关闭，允许切换机器，id={},status={},serverUrl={}",
                task.getId(), currTask.getTaskStatus(), serverUrl);
            return true;
        }

        log.info(
            "【任务分发】任务要求服务器一致,当前训练任务正在处理中，可能已经有训练缓存，不允许切换机器,id={},status={}",
            task.getId(), currTask.getTaskStatus());
        return false;
    }

    @Override
    public DispatchTypeEnum getType() {
        return DispatchTypeEnum.LORA_TRAIN;
    }

    /**
     * 检查文件夹一致性
     *
     * @param type         业务类型
     * @param task         任务
     * @param server       目标服务
     * @param originServer 原始服务
     * @return true，文件夹一致
     */
    private boolean checkFolderConsistency(DispatchTypeEnum type, IExtModel task, ServerVO server,
                                           ServerVO originServer) {
        LoraTaskParams params = ((ComfyuiTaskVO)task).getReqParams().toJavaObject(LoraTaskParams.class);
        String clothDir = params.getClothDir();
        String originFileServerUrl = serverHelper.getFileServerUrl(originServer);
        String originMd5 = comfyUIService.calcDirMd5(clothDir, originFileServerUrl);
        String targetMd5 = comfyUIService.calcDirMd5(clothDir, serverHelper.getFileServerUrl(server));

        if (StringUtils.isBlank(originMd5) || StringUtils.isBlank(targetMd5)) {
            if (StringUtils.isNotBlank(originMd5)) {
                log.info(
                    "【任务分发】任务要求服务器一致，当前服务器{}与任务服务器{}不一致，文件目录一致性校验不通过，直接跳过，并同步通知文件夹同步，type={},taskId={},originMd5={},"
                    + "targetMd5={}", server.getId(), originServer.getId(), type, task.getId(), originMd5, targetMd5);

                //MD5不相等时，通知文件夹同步
                //fileDispatch.notifyFolderSync(originFileServerUrl, clothDir, originMd5, true);
            } else {
                log.info(
                    "【任务分发】任务要求服务器一致，当前服务器{}与任务服务器{}不一致，且文件目录md5为空，直接跳过，type={},taskId={}",
                    server.getId(), originServer.getId(), type, task.getId());
            }

            return false;
        }

        if (!StringUtils.equals(originMd5, targetMd5)) {
            log.info("【任务分发】任务要求服务器一致，当前服务器{}与任务服务器{}不一致，文件目录一致性校验不通过，直接跳过，并同步通知文件夹同步，type={},taskId={},originMd5={},"
                     + "targetMd5={}", server.getId(), originServer.getId(), type, task.getId(), originMd5, targetMd5);

            //MD5不相等时，通知文件夹同步
            //fileDispatch.notifyFolderSync(originFileServerUrl, clothDir, originMd5, true);
            return false;
        }

        log.info(
            "【任务分发】任务要求服务器一致，当前服务器{}与任务服务器{}不一致，文件目录一致性校验通过，命中当前机器，type={},taskId={}",
            server.getId(), originServer.getId(), type, task.getId());
        return true;
    }

    /**
     * 获取任务服务器地址
     *
     * @param task 任务
     * @return 任务服务器地址
     */
    private String fetchTaskServerUrl(IExtModel task) {
        String serverUrl = task.getExtValue(KEY_SERVER_URL, String.class);
        Integer modelId = task.getExtValue(KEY_MODEL_ID, Integer.class);
        if (modelId == null) {
            return serverUrl;
        }
        MaterialModelVO model = materialModelService.selectById(modelId);
        ComfyuiTaskVO current = (ComfyuiTaskVO)task;
        LoraTrainDetail trainDetail = model.getClothLoraTrainDetail();
        if (trainDetail == null) {
            return serverUrl;
        }

        LoraSubTask pre = null;
        switch (current.getTaskType()) {
            case lora:
                if (current.getTaskStatus() == QueueCodeEnum.RUNNING) {
                    log.info("【任务分发】当前训练任务已经在执行中，直接返回当前url,currUrl={}", serverUrl);
                    return serverUrl;
                }
                pre = trainDetail.getLabel();
                break;
            case label:
                pre = trainDetail.getCutout();
                if (pre == null) {
                    pre = trainDetail.getPrepareView();
                }
                break;
            case cutout:
                pre = trainDetail.getPrepareView();
                break;
            default:
                return serverUrl;
        }

        String preServerUrl = null;
        if (pre != null) {
            preServerUrl = pre.getServerUrl();
        }

        if (StringUtils.isBlank(preServerUrl)) {
            log.info("【任务分发】上一次任务服务为空，返回当前url,currUrl={},preUrl={}", serverUrl, preServerUrl);
            return serverUrl;
        }

        ServerVO preServer = serverService.parseByUrl(preServerUrl);
        ServerVO currentServer = serverService.parseByUrl(serverUrl);

        if (!serverHelper.isSameMachine(preServer, currentServer)) {
            log.warn("【任务分发】当前服务地址与前一次任务不在一台机器上，使用上一次任务的地址,curr={},pre={}", serverUrl,
                preServerUrl);

            return preServerUrl;
        }

        log.info("【任务分发】当前服务地址与前一次任务在一台机器上，返回当前url,curr={},pre={}", serverUrl, preServerUrl);
        return serverUrl;
    }
}
