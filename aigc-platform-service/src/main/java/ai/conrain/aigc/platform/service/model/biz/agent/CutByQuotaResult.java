package ai.conrain.aigc.platform.service.model.biz.agent;

import ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum;
import ai.conrain.aigc.platform.service.model.vo.StyleImageRecommendation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * cutByQuota方法的返回结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CutByQuotaResult {
    private List<List<StyleImageRecommendation>> displayItems;
    private ClothShootGenreEnum genre;
}
