package ai.conrain.aigc.platform.service.model.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * CreativeElementQuery
 *
 * @version CreativeElementService.java v 0.1 2024-05-09 06:10:02
 */
@Data
public class CreativeElementQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 元素id */
    private Integer id;

    private List<Integer> ids;

    /** 名称 */
    private String name;

    /** 配置关键字，同一级配置key一致，一级要求不一致,如FACE、SCENE */
    private String configKey;

    /** 层级 */
    private Integer level;

    /** 父级id */
    private Integer parentId;

    /** 图片url */
    private String showImage;

    private Integer loraModelId;

    private String status;

    /** 排序 */
    private Integer order;

    /** 备注 */
    private String memo;

    /** 元素类型，LORA、PROMPT、FACE等 */
    private String type;

    /** type不包含 */
    private String typeNotEqual;

    private String belong;

    private Integer userId;

    /** 创建时间 */
    private Date createTime;

    /** 开始创建时间 */
    private Date startCreateTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 标签列表 */
    private String tags;

    /** 扩展标签列表 */
    private String extTags;

    /** 扩展信息 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    /** 是否为上新模特，1是，0否，默认为 0 */
    private Boolean isNew;

    private Boolean isLora;

    /** 集合列表 */
    private List<Integer> idList;

    /** 开放范围 */
    private String openScope;

    /** 换脸类型 */
    private String swapType;

    /** 性别类型 */
    private String genderType;

    /** 训练类型 */
    private String trainType;

    /** 等级列表 */
    private List<Integer> levels;

    /** 需要初始化服装类型 */
    private boolean needInitClothTypeScope;

    /** 需要初始化服装款式 */
    private boolean needInitClothCategory;

    /** 需要初始化风格类型 */
    private boolean needInitStyleType;

    /** 已生成服装类型 */
    private boolean hasClothTypeScope;

    /** 是否是风格场景 */
    private boolean styleScene;

    /** 有权限的用户id */
    private Integer scopeUserId;

    /** 父节点列表 */
    private List<Integer> parentIds;

    /** 是否训练完成 */
    private boolean trainFinished;

    /** 如果是模特则只选lora */
    private boolean faceOnlyLora;

    /** id或parent列表 */
    private List<Integer> idsOrParents;

    /** 创作类型 */
    private String creativeType;

    /** 是否专属 */
    private Boolean isExclusive;

    /** 标签列表 */
    private List<String> types;

    /** 标签列表for or */
    private List<String> typesOr;

    /** 标签列表for or list */
    private List<List<String>> typesOrList;

    /** 业务类型 */
    private String bizType;

    /** 身体部位列表 */
    private List<String> bodyTypes;

    /** 朝向列表 */
    private List<String> positions;

    /** 是否仅lora模特/场景 */
    private boolean onlyLora;

    //即将到达交付时间
    private boolean onlyNearingDelivery;

    /** 不包含的类型 */
    private List<String> excludesTypes;

    /** 性别类型列表 */
    private List<String> genderTypes;

    private String privatelyOpen2UserRoleType;
    private Integer privatelyOpen2UserId;

    /** 年龄区段 */
    private String ageRange;

    /** 年龄段列表 */
    private List<String> ageRanges;

    /** 服装款式列表 */
    private List<String> clothCategory;

    /** 配置关键字列表 */
    private List<String> configKeys;

    /** 是否仅实验标签 */
    private Boolean onlyExperimental;

    /** 是否仅不出头 */
    private Boolean onlyNoshowFace;

    /** 年龄区段(过滤项) */
    private String filterAgeRange;

    /** 关键字（FACE, SCENE, CLOTH_STYLE, REFER, LOGO_POSITION） */
    private String key;

    /** 性别类型 */
    private String clothType;

    /** 排除的父节点或id列表 */
    private List<Integer> excludesParentOrIds;

    /** 查询只含有子元素记录的数据 */
    private Boolean hasChildren;

    /** 是否展示有展示图的 */
    private Boolean isHasShowImage = false;
    /** 临时openScope结合isHasShowImage使用 */
    private String tempOpenScope;

    /** 是否与我有关的 */
    private boolean relatedToMe = false;

    /**
     * 是否未构建
     */
    private boolean notBuild = false;

    /** 风格标签 */
    private List<String> styleTypes;

    /** 渠道商关联客户id列表 */
    private List<Integer> distributorCustomerIds;

    /** 打标类型 */
    private String labelType;

    /** 打标类型不等于 */
    private String labelTypeNotEqual;
}
