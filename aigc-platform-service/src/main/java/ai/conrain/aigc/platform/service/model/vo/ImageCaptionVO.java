package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pgvector.PGvector;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * ImageCaptionVO
 *
 * @version ImageCaptionService.java v 0.1 2025-08-06 06:04:01
 */
@Data
public class ImageCaptionVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 自增主键ID */
	private Integer id;

	/** 关联的图像ID，用于跨表查询 */
	private Integer imageId;

	/** 图像标注的完整文本描述，JSON格式存储 */
	private ImageAnalysisCaption caption;

	/** 标注版本号，用于区分不同标注模型或规则 */
	private String captionVersion;

	/** 整图特征向量（1024维） */
	private PGvector imgEmb;

	/** 背景抠图特征向量（1024维） */
	private PGvector bgImgEmb;

	/** 模特面部抠图特征向量（1024维） */
	private PGvector modelFacialImgEmb;

	/** 模特姿势抠图特征向量（1024维） */
	private PGvector modelPoseImgEmb;

	/** 服装款式描述文本向量（1024维，如风格、版型） */
	private PGvector clothStyleTextEmb;

	/** 服装整体描述文本向量（1024维，如颜色、材质） */
	private PGvector clothTextEmb;

	/** 背景道具描述文本向量（1024维） */
	private PGvector bgTextEmb;

	/** 配饰描述文本向量（1024维） */
	private PGvector accessoriesTextEmb;

	/** 发型描述文本向量（1024维） */
	private PGvector hairstyleTextEmb;

	/** 姿势描述文本向量（1024维） */
	private PGvector poseTextEmb;

	/** 背景分类文本向量（1024维，用于聚类或检索） */
	private PGvector sortBgTextEmb;

	/** 表情分类文本向量（1024维，用于聚类或检索） */
	private PGvector sortFacialExpressionTextEmb;

	/** 配饰分类文本向量（1024维，用于聚类或检索） */
	private PGvector sortAccessoriesTextEmb;

	/** 姿势分类文本向量（1024维，用于聚类或检索） */
	private PGvector sortPoseTextEmb;

	/** 扩展信息，JSON格式存储非结构化附加数据 */
	private String extInfo;

	/** 记录创建时间，默认为当前时间戳 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 记录最后修改时间，默认为当前时间戳 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

	/** imageType */
	private String imageType;

	/** clothGenderType */
	private String clothGenderType;

    /**
     * ageGroup，年龄段
     */
    private String ageGroup;

    /**
     * qualityScore，质量分
     */
    private Double qualityScore;

    /**
     * genre，流派
     */
    private String genre;

	//----------------------view columns----------------------
    //余弦相似度，取值范围是[-1,1]，其中，1表示完全相似，-1表示完全相反，0表示毫不相关；余弦相似度=pgvector的1-cos(x,y)，其中cos(x,y)指定的是<=>得到的余弦距离（取值范围[0,2]），注意需要和<=>配合使用
    private Double clothStyleSimilarity;
}