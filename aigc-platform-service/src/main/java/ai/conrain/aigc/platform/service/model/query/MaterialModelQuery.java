package ai.conrain.aigc.platform.service.model.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.validation.SimpleDate;
import lombok.Data;

/**
 * MaterialModelQuery
 *
 * @version MaterialModelService.java v 0.1 2024-05-09 06:10:02
 */
@Data
public class MaterialModelQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    private List<Integer> ids;

    /** 名称 */
    private String name;

    private String nameLike;

    // all,unset,手机号
    private String relatedOperatorType;

    private Integer relatedOrOperatorId;

    private String type;

    /** 归属主账号id */
    private Integer userId;

    private List<Integer> userIds;

    private Integer distributorMasterId;

    /** 展示图url */
    private String showImage;

    /** lora名称 */
    private String loraName;

    /**
     * 状态，ENABLED、DISABLED、IN_TRAINING
     *
     * @see MaterialModelStatusEnum
     */
    private String status;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 开始创建时间 */
    private Date startCreateTime;

    /** 修改时间 */
    private Date modifyTime;

    // 起始日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateFrom;

    // 截止日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateTo;

    /** 标签列表 */
    private String tags;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    private Boolean isOwner;

    private boolean needTrainDetail;
    private boolean needInitClothCategory;

    private boolean needModelPoint;

    private List<String> statusList;

    private String clothStyleType;

    /** 主子类型 */
    private String mainType;

    /** 主模型id */
    private Integer mainId;

    /** 主子类型列表 */
    private List<String> mainTypes;

    // 是否只查询未确认的lora
    private boolean onlyUnconfirmedLora = false;

    // 是否只显示销售
    private boolean onlyShowSaleLora = false;

    private String nameOrUserLike;

    /** 是否只展示flux新版本的模型 */
    private boolean onlyShowV2 = false;

    /** 是否只展示有客户案例的模型 */
    private boolean onlyShowHasCase = false;

    // 素材类型，cloth/face等
    private String materialType;

    /** 服装类型 */
    private String garmentType;

    /** 服装类型列表 */
    private List<String> garmentTypeList;

    /** 打标方式 */
    private String labelType;

    private boolean onlyShowDemo = false;

    /** 只展示临近交付 */
    private boolean onlyNearingDelivery = false;

    /** 只展示交付超时 */
    private boolean onlyDeliveryTimeout = false;

    /** 是否需要同步测试图片状态 */
    private boolean needSyncTestImageStatus;

    /** 审核状态 */
    private String reviewStatus;

    /** 审核类型 */
    private String reviewType;

    /** 交付类型。人工交付/自动交付 */
    private String deliveryMode;


    /** 是否可以初审 */
    private boolean initialReviewable = false;

    /** 是否在审核中 */
    private boolean testing = false;

    /** 只显示我相关的 */
    private boolean relatedToMe = false;

    /** 当前用户id */
    private Integer currentUserId;

    /** 审核员id */
    private Integer reviewerId;

    /** prompt工程师 */
    private Integer promptUserId;

    /** 仅查看实验的 */
    private Boolean onlyExperimental;

    /** 隐藏测试中且为实验模型的数据（商家视角） */
    private Boolean hideTestExperimental = false;

    /** 仅展示多色拆分的 */
    private Boolean onlyShowColorSplit;

    /** 仅查看已处理的 */
    private Boolean onlyProcessed;

    /** 仅查看有问题的 */
    private Boolean onlyProblematic;

    /** 年龄范围 */
    private String ageRange;

    /** 年龄段列表 */
    private List<String> ageRanges;

    /** 起始时间 */
    private String startDate;

    /** 截止时间 */
    private String endDate;

    /** 名称列表 */
    private Set<String> nameList;

    /** 是否忽略拷贝服装 */
    private Boolean isIgnoreCopy;

    /** 交付时间段起始时间 */
    private String deliveryStartDate;

    /** 交付时间段截止时间 */
    private String deliveryEndDate;
}
