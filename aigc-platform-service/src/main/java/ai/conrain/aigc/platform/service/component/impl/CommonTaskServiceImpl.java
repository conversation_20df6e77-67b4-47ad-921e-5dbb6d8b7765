package ai.conrain.aigc.platform.service.component.impl;

import javax.imageio.ImageIO;

import com.alibaba.fastjson2.JSONObject;

import ai.conrain.aigc.platform.dal.dao.CommonTaskDAO;
import ai.conrain.aigc.platform.dal.entity.CommonTaskDO;
import ai.conrain.aigc.platform.dal.example.CommonTaskExample;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.taobao.huiwa.HuiWaService;
import ai.conrain.aigc.platform.integration.taobao.huiwa.model.request.HuiwaModelQueryReq;
import ai.conrain.aigc.platform.integration.taobao.huiwa.model.request.HuiwaTaskImageCreativeRequest;
import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.component.CreativeTaskService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.enums.CommonTaskEnums;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CommonTaskConverter;
import ai.conrain.aigc.platform.service.model.query.CommonTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.FileUtils;
import ai.conrain.aigc.platform.service.util.ImageUtils;
import com.taobao.api.response.LianfanHuiwaModelGetResponse;
import com.taobao.api.response.LianfanHuiwaTaskImageGetResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.HUI_WA_PIC_QUALITY;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_PREFERENCE_IS_UPLOAD_SUCCESS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_PREFERENCE_MODEL_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TASK_INDEX;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.NO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.SHOW_CLOTHES_PIC;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;

/**
 * CommonTaskService实现
 */
@Slf4j
@Service
public class CommonTaskServiceImpl implements CommonTaskService {

    /**
     * DAO
     */
    @Autowired
    private CommonTaskDAO commonTaskDAO;

    @Autowired
    private HuiWaService huiWaService;

    @Lazy
    @Autowired
    private CreativeTaskService creativeTaskService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private OssService ossService;

    @Override
    public CommonTaskVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CommonTaskDO data = commonTaskDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return CommonTaskConverter.do2VO(data);
    }

    @Override
    public CommonTaskVO lockById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CommonTaskDO data = commonTaskDAO.lockById(id);
        if (null == data) {
            return null;
        }

        return CommonTaskConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = commonTaskDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除CommonTask失败");
    }

    @Override
    public CommonTaskVO insert(CommonTaskVO commonTask) {
        AssertUtil.assertNotNull(commonTask, ResultCode.PARAM_INVALID, "commonTask is null");
        AssertUtil.assertTrue(commonTask.getId() == null, ResultCode.PARAM_INVALID, "commonTask.id is present");

        //创建时间、修改时间兜底
        if (commonTask.getCreateTime() == null) {
            commonTask.setCreateTime(new Date());
        }

        if (commonTask.getModifyTime() == null) {
            commonTask.setModifyTime(new Date());
        }

        CommonTaskDO data = CommonTaskConverter.vo2DO(commonTask);
        //逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        Integer n = commonTaskDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建CommonTask失败");
        AssertUtil.assertNotNull(data.getId(), "新建CommonTask返回id为空");
        commonTask.setId(data.getId());
        return commonTask;
    }


    @Override
    public void updateByIdSelective(CommonTaskVO commonTask) {
        AssertUtil.assertNotNull(commonTask, ResultCode.PARAM_INVALID, "commonTask is null");
        AssertUtil.assertTrue(commonTask.getId() != null, ResultCode.PARAM_INVALID, "commonTask.id is null");

        //修改时间必须更新
        commonTask.setModifyTime(new Date());
        CommonTaskDO data = CommonTaskConverter.vo2DO(commonTask);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = commonTaskDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新CommonTask失败，影响行数:" + n);
    }

    @Override
    public List<CommonTaskVO> queryCommonTaskList(CommonTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        CommonTaskExample example = CommonTaskConverter.query2Example(query);

        List<CommonTaskDO> list = commonTaskDAO.selectByExampleWithBLOBs(example);
        return CommonTaskConverter.doList2VOList(list);
    }

    @Override
    public Long queryCommonTaskCount(CommonTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        CommonTaskExample example = CommonTaskConverter.query2Example(query);
        long c = commonTaskDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询通用任务
     */
    @Override
    public PageInfo<CommonTaskVO> queryCommonTaskByPage(CommonTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<CommonTaskVO> page = new PageInfo<>();

        CommonTaskExample example = CommonTaskConverter.query2Example(query);
        long totalCount = commonTaskDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<CommonTaskDO> list = commonTaskDAO.selectByExampleWithBLOBs(example);
        page.setList(CommonTaskConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public void syncThirdTaskStatus(CreativeTaskVO task) {
        // 暂时不做通用处理

        // 绘蛙出图检测
        if (task.getType().equals(CreativeTypeEnum.HUIWA_BASIC_CHANGING_CLOTHES)) {
            // 提取creativeId
            Integer id = task.getId();
            // 获取其关联的 commonTask
            CommonTaskQuery query = new CommonTaskQuery();
            query.setRelatedBizId(String.valueOf(id));

            // 查询三方任务列表
            List<CommonTaskVO> commonTaskVOList = queryCommonTaskList(query);
            if (commonTaskVOList.isEmpty() || commonTaskVOList.getFirst() == null) {
                log.error("【三方任务同步】【绘蛙任务】CommonTaskServiceImpl::syncThirdTaskStatus::绘蛙三方任务不存在，同步结束...");
                return;
            }
            CommonTaskVO commonTaskVO = commonTaskVOList.getFirst();

            // 获取任务参考图是否已经上传完成
            String preferenceIsUploadSuccess = commonTaskVO.getStringFromExtInfo(KEY_PREFERENCE_IS_UPLOAD_SUCCESS);
            // 若为空或者为N，则说明参考图状态尚未同步，需要查询状态进行同步
            if (StringUtils.isEmpty(preferenceIsUploadSuccess) || preferenceIsUploadSuccess.equals(NO)) {
                // 同步模型状态
                huiwaModelStatusSync(task, commonTaskVO);
                return;
            }

            // 当参考图是否已经上传完成 且 外部任务ID为空 时再进行任务上传
            String outTaskId = commonTaskVO.getOutTaskId();
            if (preferenceIsUploadSuccess.equals(YES) && StringUtils.isEmpty(outTaskId)) {
                // 创建绘蛙出图任务
                huiwaCreativeImageTask(task, commonTaskVO);
            }

            // 任务状态同步
            huiwaTaskSync(task, commonTaskVO);
        }
    }


    /**
     * 绘蛙模型状态同步
     *
     * @param task         任务信息
     * @param commonTaskVO 三方任务信息
     */
    private void huiwaModelStatusSync(CreativeTaskVO task, CommonTaskVO commonTaskVO) {
        // 1、提取 ModelId 并强转为Long 类型
        Long modelId = Long.valueOf(commonTaskVO.getExtInfo(KEY_PREFERENCE_MODEL_ID));

        // 2、封装查询条件
        HuiwaModelQueryReq huiwaModelQueryReq = HuiwaModelQueryReq.builder()
                .modelIds(Collections.singletonList(modelId))
                .type(3L)
                .source(1L)
                .build();
        List<LianfanHuiwaModelGetResponse.ModelDTO> modelDTOList = huiWaService.queryModelStatus(huiwaModelQueryReq);
        if (modelDTOList == null || modelDTOList.isEmpty()) {
            log.error("【绘蛙】【参考图状态查询】CommonTaskServiceImpl::huiwaModelStatusSync::模型状态查询失败,同步终止...");
            return;
        }

        // 3、条件分析
        // 默认只取第一个
        LianfanHuiwaModelGetResponse.ModelDTO modelDTO = modelDTOList.get(0);

        // 模型训练状态值，1:排队中，2:生成中，4:生成成功，-1:生成失败
        Long statusValue = modelDTO.getTaskStatus().getStatusValue();

        if (statusValue == 4L) {
            // 打上标标识参考图模型已经训练完成
            commonTaskVO.addExtInfo(KEY_PREFERENCE_IS_UPLOAD_SUCCESS, YES);
        } else if (statusValue == -1L) {
            // 状态置为失败
            commonTaskVO.setTaskStatus(CommonTaskEnums.TaskStatus.FAILED.name());
            commonTaskVO.setOutTaskStatus(CommonTaskEnums.TaskStatus.FAILED.name());
            //  creativeTask的状态也置为失败
            task.setStatus(CreativeStatusEnum.FAILED);

            // 更新 creativeTask 信息
            creativeTaskService.updateByIdSelective(task);
        }

        // 更新 CommonTask 信息
        updateByIdSelective(commonTaskVO);
    }

    /**
     * 创建绘蛙出图任务
     *
     * @param task         内部任务
     * @param commonTaskVO 三方任务
     */
    private void huiwaCreativeImageTask(CreativeTaskVO task, CommonTaskVO commonTaskVO) {
        // 1、提取参数
        String clotheType = commonTaskVO.getStringFromExtInfo(KEY_CLOTH_TYPE);
        String showClothedPic = commonTaskVO.getStringFromExtInfo(SHOW_CLOTHES_PIC);
        Long modelId = Long.valueOf(commonTaskVO.getStringFromExtInfo(KEY_PREFERENCE_MODEL_ID));

        // 2、查询系统配置
        List<Integer> qualityList = Arrays.asList(1, 2);
        List<Integer> activePicQualityList = systemConfigService.queryHuiWaConfig(HUI_WA_PIC_QUALITY, qualityList);

        // 3、获取当前任务激活的质量水平
        Integer index = task.getIntegerFromExtInfo(KEY_TASK_INDEX);
        Integer activePicQuality = activePicQualityList.get(index);

        // 3、构建参数
        HuiwaTaskImageCreativeRequest request = HuiwaTaskImageCreativeRequest.builder()
                .clothType(clotheType)
                .clothImage(showClothedPic)
                .modelId(modelId)
                .taskQuality(activePicQuality.longValue())
                .build();

        // 4、发送请求
        Long imageTaskId = huiWaService.createImageTask(request);
        if (imageTaskId == null) {
            log.error("【绘蛙】【创建出图任务】CommonTaskServiceImpl::huiwaCreativeImageTask::创建出图任务API异常，任务终止...");
            // 状态置为失败
            commonTaskVO.setTaskStatus(CommonTaskEnums.TaskStatus.FAILED.name());
            commonTaskVO.setOutTaskStatus(CommonTaskEnums.TaskStatus.FAILED.name());
            //  creativeTask的状态也置为失败
            task.setStatus(CreativeStatusEnum.FAILED);

            // 更新 creativeTask 信息
            creativeTaskService.updateByIdSelective(task);
            // 更新 CommonTask 信息
            updateByIdSelective(commonTaskVO);
            return;
        }

        // 设置三方任务 id
        commonTaskVO.setOutTaskId(String.valueOf(imageTaskId));
        // 设置三方任务状态为初始化
        commonTaskVO.setOutTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
        // 设置任务开始时间
        commonTaskVO.setTaskStartTime(new Date());
        // 更新 CommonTask 信息
        updateByIdSelective(commonTaskVO);
    }

    /**
     * 绘蛙任务状态检测
     *
     * @param creativeTask 任务信息
     * @param commonTaskVO 三方任务信息
     */
    void huiwaTaskSync(CreativeTaskVO creativeTask, CommonTaskVO commonTaskVO) {
        if (commonTaskVO == null || commonTaskVO.getOutTaskId() == null) {
            log.error("【三方任务同步】【绘蛙任务】CommonTaskServiceImpl::syncThirdTaskStatus::绘蛙任务ID为空，同步结束...");
            return;
        }

        // 查询三方任务状态
        LianfanHuiwaTaskImageGetResponse.BaseImageTaskDTO baseImageTaskDTO = huiWaService.queryTaskStatus(Integer.valueOf(commonTaskVO.getOutTaskId()));
        if (baseImageTaskDTO == null) {
            log.error("【三方任务同步】【绘蛙任务】CommonTaskServiceImpl::syncThirdTaskStatus::绘蛙任务查询失败，同步结束...");
            return;
        }

        // 解析状态和结果
        LianfanHuiwaTaskImageGetResponse.ImageTaskStatusDTO huiwaTaskStatus = baseImageTaskDTO.getTaskStatus();
        CommonTaskEnums.TaskStatus taskStatus = CommonTaskEnums.TaskStatus.fromHuiwaTaskStatus(huiwaTaskStatus.getStatusValue());

        // 更新 CommonTask 状态
        commonTaskVO.setOutTaskStatus(String.valueOf(huiwaTaskStatus.getStatusValue()));
        commonTaskVO.setTaskStatus(taskStatus.name());
        // 更新 CreativeTask 状态
        creativeTask.setStatus(CreativeStatusEnum.getCreativeStatusByCommonTaskStatus(taskStatus));

        // 成功和失败进行单独处理，其余状态直接更新即可
        if (taskStatus.equals(CommonTaskEnums.TaskStatus.COMPLETED)) {
            // 三方任务设置结果
            commonTaskVO.setRetDetail(JSONObject.toJSONString(baseImageTaskDTO));
            // 三方任务设置结束时间
            commonTaskVO.setTaskEndTime(new Date());

            // 最终结果图片
            String imageUrl = baseImageTaskDTO.getImageUrl();

            // 此处对最终结果进行转换，转换为我们 oss 的地址
            try {
                URL url = new URL(imageUrl);

                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
                // 强制使用 .jpg 扩展名
                String ossImageName = CommonUtil.uuid() + ".jpg";
                String ossFullName = sdf.format(new Date()) + creativeTask.getOperatorId() + "/" + ossImageName;

                // 下载原始图片并转换为JPG格式
                try (InputStream in = url.openStream()) {
                    BufferedImage originalImage = ImageIO.read(in);
                    if (originalImage != null) {
                        // 转换为JPG格式（去除透明度，使用白色背景）
                        BufferedImage jpgImage = FileUtils.convertPngToJpg(originalImage);

                        // 使用高质量JPG保存
                        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                            ImageUtils.saveFullQualityJPG(jpgImage, baos);
                            byte[] jpgBytes = baos.toByteArray();

                            // 上传转换后的JPG图片到OSS
                            try (ByteArrayInputStream jpgInputStream = new ByteArrayInputStream(jpgBytes)) {
                                imageUrl = ossService.upload(ossFullName, jpgInputStream);
                            }
                        }
                    } else {
                        log.warn("无法读取图片，使用原始流上传: {}", imageUrl);
                        try (InputStream retryIn = url.openStream()) {
                            imageUrl = ossService.upload(ossFullName, retryIn);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("上传图片到OSS失败,使用默认数据imageUrl: {}", imageUrl, e);
                throw new RuntimeException(e);
            }

            // 设置最终结果
            creativeTask.setResultImages(Collections.singletonList(imageUrl));
        }

        // 1、更新 CommonTak 状态信息
        updateByIdSelective(commonTaskVO);

        // 2、更新 CreativeTask 状态信息
        creativeTaskService.updateByIdSelective(creativeTask);
    }
}