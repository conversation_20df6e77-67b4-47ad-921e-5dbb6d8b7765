package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

@Getter
public enum ClothGenderEnum {
    MALE("Male", "男"),
    FEMALE("Female", "女"),
    UNISEX("Unisex", "通用"),
    UNKNOWN("Unknown", "未知"),
    ;

    private String code;
    private String desc;

    ClothGenderEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ClothGenderEnum getByCode(String code) {
        for (ClothGenderEnum type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }
}
