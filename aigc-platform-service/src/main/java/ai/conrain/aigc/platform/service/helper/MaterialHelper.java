/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.helper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.model.QueueResult.QueueCodeEnum;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_GARMENT_CATEGORY;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.CUTOUT_HD_GARMENT_CATEGORIES;

/**
 * 素材处理帮助类
 *
 * <AUTHOR>
 * @version : MaterialHelper.java, v 0.1 2025/8/7 15:33 renxiao.wu Exp $
 */
@Slf4j
@Component
public class MaterialHelper {
    private final static String CUTOUT_HD_SIZE = "1536";
    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 基于预处理的结果前置处理detail信息
     *
     * @param prepareViewTask 预处理结果
     * @param detail          训练详情
     */
    public void preProcessCutout(ComfyuiTaskVO prepareViewTask, LoraTrainDetail detail) {
        if (detail.getCutoutAgain() != null && detail.getCutoutAgain()) {
            log.info("当前属于重新抠图打标，不重置image");
            return;
        }

        if (StringUtils.equals(CUTOUT_HD_SIZE, detail.getImageSize())) {
            return;
        }

        //已经在抠图执行中的则不进行设置
        if (detail.getCutout() != null && (detail.getCutout().getStatus() == QueueCodeEnum.RUNNING
                                           || detail.getCutout().getStatus() == QueueCodeEnum.COMPLETED
                                           || detail.getCutout().getStatus() == QueueCodeEnum.QUEUED)) {
            return;
        }

        JSONObject prepareViewRet = JSONObject.parseObject(prepareViewTask.getRetDetail());
        if (prepareViewRet == null) {
            return;
        }

        String garmentCategory = prepareViewRet.getString(KEY_GARMENT_CATEGORY);
        if (StringUtils.isBlank(garmentCategory)) {
            return;
        }

        JSONArray list = systemConfigService.queryJsonArrValue(CUTOUT_HD_GARMENT_CATEGORIES);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        boolean isHit = false;
        for (Object item : list) {
            if (StringUtils.containsIgnoreCase(garmentCategory, (String)item)) {
                isHit = true;
                break;
            }
        }

        if (isHit) {
            log.info("当前属于{}服装款式,命中配置,设置imageSize为1536", garmentCategory);
            detail.setImageSize(CUTOUT_HD_SIZE);
            return;
        }

        log.info("当前属于{}服装款式,未命中抠图尺寸1536配置", garmentCategory);
    }
}
