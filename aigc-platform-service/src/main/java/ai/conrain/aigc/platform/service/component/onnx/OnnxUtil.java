package ai.conrain.aigc.platform.service.component.onnx;

import ai.conrain.aigc.platform.service.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

@Slf4j
public class OnnxUtil {

    /**
     * 加载和预处理图像
     *
     * @param imagePath 图像文件路径
     * @return 预处理后的图像数据
     */
    public static float[][][][] loadAndPreprocessImage(String imagePath) {
        try {
            // 使用Java内置ImageIO加载图像
            BufferedImage originalImage = ImageIO.read(new java.io.File(imagePath));

            if (originalImage == null) {
                throw new RuntimeException("无法加载图像: " + imagePath);
            }

            log.debug("原始图像尺寸: {}x{}", originalImage.getWidth(), originalImage.getHeight());

            // 调整图像尺寸到模型要求的224x224
            BufferedImage resizedImage = new BufferedImage(224, 224, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = resizedImage.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.drawImage(originalImage, 0, 0, 224, 224, null);
            g2d.dispose();

            log.debug("调整后尺寸: 224x224");

            // 转换为float数组
            float[][][][] imageData = new float[1][3][224][224];

            // 提取像素数据
            for (int h = 0; h < 224; h++) {
                for (int w = 0; w < 224; w++) {
                    int rgb = resizedImage.getRGB(w, h);
                    // 提取RGB通道，值范围保持[0, 255]
                    imageData[0][0][h][w] = (float) ((rgb >> 16) & 0xFF) / 255.0f; // [0, 1]
                    imageData[0][1][h][w] = (float) ((rgb >> 8) & 0xFF) / 255.0f;  // [0, 1]
                    imageData[0][2][h][w] = (float) (rgb & 0xFF) / 255.0f;         // [0, 1]
                }
            }

            return imageData;

        } catch (IOException e) {
            throw new RuntimeException("加载图像失败: " + imagePath, e);
        }
    }

    /**
     * 从URL下载图片到本地临时文件
     *
     * @param imageUrl 图片URL
     * @return 本地临时文件路径
     * @throws IOException 下载异常
     */
    public static Path downloadImageFromUrl(String imageUrl) throws IOException {
        // 从URL中提取文件扩展名，默认为.jpg
        String extension = ".jpg";
        if (imageUrl.contains(".")) {
            String urlPath = imageUrl.split("\\?")[0]; // 去除URL参数
            int lastDotIndex = urlPath.lastIndexOf(".");
            if (lastDotIndex > 0 && lastDotIndex < urlPath.length() - 1) {
                String ext = urlPath.substring(lastDotIndex);
                if (ext.matches("\\.(jpg|jpeg|png|gif|bmp|webp)")) {
                    extension = ext;
                }
            }
        }

        // 创建临时文件
        Path tempFile = Files.createTempFile(CommonUtil.uuid(), extension);

        // 下载图片
        try (InputStream inputStream = new URL(imageUrl).openStream()) {
            Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);
        }

        return tempFile;
    }

    /**
     * 清除临时文件
     *
     * @param tempFile 临时文件路径
     */
    public static void cleanupTempFile(Path tempFile) {
        if (tempFile != null) {
            try {
                Files.deleteIfExists(tempFile);
                log.debug("临时文件已清理: {}", tempFile);
            } catch (Exception e) {
                log.warn("清理临时文件失败: {}", tempFile, e);
            }
        }
    }
}
