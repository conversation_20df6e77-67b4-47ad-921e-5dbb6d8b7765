package ai.conrain.aigc.platform.service.component.onnx;

import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.twelvemonkeys.imageio.plugins.webp.WebPImageReaderSpi;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import javax.imageio.spi.IIORegistry;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;

@Slf4j
public class OnnxUtil {

    static {
        // Register WebP image reader to enable WebP format support
        try {
            IIORegistry registry = IIORegistry.getDefaultInstance();
            registry.registerServiceProvider(new WebPImageReaderSpi());
            log.debug("WebP ImageIO plugin registered successfully");
            log.debug("Supported image formats: {}", Arrays.toString(ImageIO.getReaderFormatNames()));
        } catch (Exception e) {
            log.warn("Failed to register WebP ImageIO plugin: {}", e.getMessage());
        }
    }

    /**
     * 加载和预处理图像，支持包括WebP在内的多种图像格式
     *
     * @param imagePath 图像文件路径
     * @return 预处理后的图像数据
     */
    public static float[][][][] loadAndPreprocessImage(String imagePath) {
        try {
            // 检查文件是否存在
            java.io.File imageFile = new java.io.File(imagePath);
            if (!imageFile.exists()) {
                throw new RuntimeException("图像文件不存在: " + imagePath);
            }

            log.debug("正在加载图像: {}, 文件大小: {} bytes", imagePath, imageFile.length());

            // 使用Java内置ImageIO加载图像，现在支持WebP格式
            BufferedImage originalImage = ImageIO.read(imageFile);

            if (originalImage == null) {
                // 提供更详细的错误信息
                String[] supportedFormats = ImageIO.getReaderFormatNames();
                log.error("无法加载图像: {}，支持的格式: {}", imagePath, Arrays.toString(supportedFormats));
                throw new RuntimeException("无法加载图像，可能是不支持的格式: " + imagePath);
            }

            log.debug("成功加载图像，原始尺寸: {}x{}, 类型: {}",
                    originalImage.getWidth(), originalImage.getHeight(), originalImage.getType());

            // 调整图像尺寸到模型要求的224x224
            BufferedImage resizedImage = new BufferedImage(224, 224, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = resizedImage.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.drawImage(originalImage, 0, 0, 224, 224, null);
            g2d.dispose();

            log.debug("图像已调整为标准尺寸: 224x224");

            // 转换为float数组
            float[][][][] imageData = new float[1][3][224][224];

            // 提取像素数据并归一化到[0, 1]范围
            for (int h = 0; h < 224; h++) {
                for (int w = 0; w < 224; w++) {
                    int rgb = resizedImage.getRGB(w, h);
                    // 提取RGB通道，值范围归一化到[0, 1]
                    imageData[0][0][h][w] = (float) ((rgb >> 16) & 0xFF) / 255.0f; // Red
                    imageData[0][1][h][w] = (float) ((rgb >> 8) & 0xFF) / 255.0f; // Green
                    imageData[0][2][h][w] = (float) (rgb & 0xFF) / 255.0f; // Blue
                }
            }

            log.debug("图像预处理完成，数据维度: [1][3][224][224]");
            return imageData;

        } catch (IOException e) {
            log.error("加载图像时发生IO异常: {}", imagePath, e);
            throw new RuntimeException("加载图像失败: " + imagePath, e);
        } catch (Exception e) {
            log.error("预处理图像时发生异常: {}", imagePath, e);
            throw new RuntimeException("预处理图像失败: " + imagePath, e);
        }
    }

    /**
     * 从URL下载图片到本地临时文件
     *
     * @param imageUrl 图片URL
     * @return 本地临时文件路径
     * @throws IOException 下载异常
     */
    public static Path downloadImageFromUrl(String imageUrl) throws IOException {
        // 从URL中提取文件扩展名，默认为.jpg
        String extension = ".jpg";
        if (imageUrl.contains(".")) {
            String urlPath = imageUrl.split("\\?")[0]; // 去除URL参数
            int lastDotIndex = urlPath.lastIndexOf(".");
            if (lastDotIndex > 0 && lastDotIndex < urlPath.length() - 1) {
                String ext = urlPath.substring(lastDotIndex);
                if (ext.matches("\\.(jpg|jpeg|png|gif|bmp|webp)")) {
                    extension = ext;
                }
            }
        }

        // 创建临时文件
        Path tempFile = Files.createTempFile(CommonUtil.uuid(), extension);

        // 下载图片
        try (InputStream inputStream = new URL(imageUrl).openStream()) {
            Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);
        }

        return tempFile;
    }

    /**
     * 清除临时文件
     *
     * @param tempFile 临时文件路径
     */
    public static void cleanupTempFile(Path tempFile) {
        if (tempFile != null) {
            try {
                Files.deleteIfExists(tempFile);
                log.debug("临时文件已清理: {}", tempFile);
            } catch (Exception e) {
                log.warn("清理临时文件失败: {}", tempFile, e);
            }
        }
    }
}
