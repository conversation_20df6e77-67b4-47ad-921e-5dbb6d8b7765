/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz;

import java.io.Serializable;
import lombok.Data;

/**
 * 服装颜色明细
 *
 * <AUTHOR>
 * @version : ClothColorDetail.java, v 0.1 2024/10/15 14:59 renxiao.wu Exp $
 */
@Data
public class ClothColorDetail implements Serializable {
    private static final long serialVersionUID = -9036131994621005142L;
    /** 索引位置，从1开始 */
    private int index;
    /** 英文名称 */
    private String name;
    /** 中文说明 */
    private String desc;
    /** 展示图片 */
    private String showImg;
    /** 详细描述 */
    private String value;
    /** 服装类型 */
    private String garmentType;
    /** 明细类型 */
    private String detailType;
    /** 明细补充描述 */
    private String detailDesc;
    /** 前缀 */
    private String prefix;
    /** 是否可用 */
    private boolean enable = true;
    /** 服装状态 */
    private String garmentStatus;
    /** 分析结果json */
    private String analysisJson;
    /** 服装原始搭配 */
    private String views;

    public ClothColorDetail() {}

    public ClothColorDetail(int index, String name, String value) {
        this(index, name, null, null, value, null, null, null, null, null, null);
    }

    public ClothColorDetail(int index, String name, String value, String garmentType, String detailType,
                            String detailDesc, String prefix, String garmentStatus, String views) {
        this(index, name, null, null, value, garmentType, detailType, detailDesc, prefix, garmentStatus, views);
    }

    public ClothColorDetail(int index, String name, String value, String garmentType, String prefix, String views,
                            String analysisJson) {
        //new ClothColorDetail(index, top.getColor(), extTags, top.getType(), null, null,
        //bodyPosition.getExtTag(), null, views)
        this(index, name, value, garmentType, null, null, prefix, null, views);
        this.analysisJson = analysisJson;
    }

    public ClothColorDetail(int index, String name, String desc, String showImg, String value, String garmentType,
                            String detailType, String detailDesc, String prefix, String garmentStatus, String views) {
        this.index = index;
        this.name = name;
        this.desc = desc;
        this.showImg = showImg;
        this.value = value;
        this.garmentType = garmentType;
        this.detailType = detailType;
        this.detailDesc = detailDesc;
        this.prefix = prefix;
        this.garmentStatus = garmentStatus;
        this.views = views;
    }
}
