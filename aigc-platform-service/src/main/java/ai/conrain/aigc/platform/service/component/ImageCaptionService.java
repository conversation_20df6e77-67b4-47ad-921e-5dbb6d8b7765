package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisResult;
import ai.conrain.aigc.platform.service.enums.AgeGroupEnum;
import ai.conrain.aigc.platform.service.enums.ClothGenderEnum;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageCandidate;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import com.pgvector.PGvector;

import java.util.List;
import java.util.Set;

/**
 * 图像标注 Service定义
 *
 * <AUTHOR>
 * @version ImageCaptionService.java v 0.1 2025-07-25 11:33:21
 */
public interface ImageCaptionService {
	
	/**
	 * 查询图像标注对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ImageCaptionVO selectById(Integer id);

	/**
	 * 删除图像标注对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加图像标注对象
	 * @param imageCaption 对象参数
	 * @return 返回结果
	 */
	ImageCaptionVO insert(ImageCaptionVO imageCaption);

	/**
	 * 修改图像标注对象
	 * @param imageCaption 对象参数
	 */
	void updateByIdSelective(ImageCaptionVO imageCaption);

	/**
	 * 带条件批量查询图像标注列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<ImageCaptionVO> queryImageCaptionList(ImageCaptionQuery query);

	List<ImageCaptionVO> queryImageCaptionList4Recall(ImageCaptionQuery query);

	/**
	 * 带条件查询图像标注数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryImageCaptionCount(ImageCaptionQuery query);

	/**
	 * 带条件分页查询图像标注
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<ImageCaptionVO> queryImageCaptionByPage(ImageCaptionQuery query);

	/**
     * 根据款式向量相似度查询图像打标列表
     *
     * @param styleVector         款式向量
     * @param gender              性别
     * @param similarityThreshold 相似度阈值
     * @param limit               限制返回数量
     * @param genre
     * @param ageGroup
     * @param excludeImageCaptionIds 需要排除的图像标注ID集合
     * @return 匹配的图像打标列表
     */
	List<ImageCaptionVO> queryByStyleVectorSimilarity(PGvector styleVector, ClothGenderEnum gender,
                                                      double similarityThreshold, int limit, String genre, AgeGroupEnum ageGroup, Set<Integer> excludeImageCaptionIds);

	/**
     * 从缓存中根据款式向量相似度查询图像打标列表（优化版本）
     * 如果缓存未初始化，则回退到数据库查询
     *
     * @param styleVector         款式向量
     * @param gender              性别
     * @param similarityThreshold 相似度阈值
     * @param limit               限制返回数量
     * @param genre               流派
     * @param ageGroup
     * @param excludeImageCaptionIds 需要排除的图像标注ID集合
     * @return 匹配的图像打标列表
     */
	List<ImageCaptionVO> queryByStyleVectorSimilarityFromCache(PGvector styleVector, ClothGenderEnum gender,
                                                               double similarityThreshold, int limit, String genre, AgeGroupEnum ageGroup, Set<Integer> excludeImageCaptionIds);

	/**
	 * 初始化缓存（应用启动时调用）
	 */
	void initCacheOnStartup();

	/**
	 * 等待缓存初始化完成（仅用于测试）
	 * @param maxWaitSeconds 最大等待时间（秒）
	 * @return 是否初始化完成
	 */
	boolean waitForCacheInitialization(int maxWaitSeconds);

	/**
	 * 根据结构化描述构造服装文本
	 */
	String getClothFeatureTextByAnalysis(ImageAnalysisCaption clothAnalysis, ClothTypeEnum clothType);

	/**
	 * 根据结构化描述构造服装款式文本（用于召回）
	 * @param captionModel
	 * @return
	 */
	String getClothStyleDescription(ImageAnalysisCaption captionModel);

	PGvector calcClothTextVector(ImageAnalysisCaption clothAnalysis, ClothTypeEnum clothType);

	void fillSortVectors(List<StyleImageCandidate> candidates);

    String createImageCaptionTaskWithNotify(ImageVO imageVO);

    void onCaptionSuccess(ImageAnalysisResult imageAnalysisResult);

    /**
     * 根据图像信息创建图像打标对象
     *
     * @param imageVO
     * @param initEmbeddings
     * @param ignoreNoCaption
     * @return
     */
    ImageCaptionVO createImageCaptionEmbeddings(ImageVO imageVO, boolean initEmbeddings, boolean ignoreNoCaption);

	/**
	 * 计算图像打标向量
	 *
	 * @param imageCaption
	 * @param skipSortEmbedding
	 */
	void initEmbeddingsByCaption(ImageCaptionVO imageCaption, ClothTypeEnum clothTypeEnum, boolean skipSortEmbedding);
}