package ai.conrain.aigc.platform.service.model.query;

import lombok.Data;
import com.pgvector.PGvector;

import java.util.Date;

import java.io.Serializable;

/**
 * ImageCaptionQuery
 *
 * @version ImageCaptionService.java v 0.1 2025-08-06 06:04:01
 */
@Data
public class ImageCaptionQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 自增主键ID */
    private Integer id;

    /** 关联的图像ID，用于跨表查询 */
    private Integer imageId;

    /** 图像标注的完整文本描述，JSON格式存储 */
    private String caption;

    /** 标注版本号，用于区分不同标注模型或规则 */
    private String captionVersion;

    /** 整图特征向量（1024维） */
    private PGvector imgEmb;

    /** 背景抠图特征向量（1024维） */
    private PGvector bgImgEmb;

    /** 模特面部抠图特征向量（1024维） */
    private PGvector modelFacialImgEmb;

    /** 模特姿势抠图特征向量（1024维） */
    private PGvector modelPoseImgEmb;

    /** 服装款式描述文本向量（1024维，如风格、版型） */
    private PGvector clothStyleTextEmb;

    /** 服装整体描述文本向量（1024维，如颜色、材质） */
    private PGvector clothTextEmb;

    /** 背景道具描述文本向量（1024维） */
    private PGvector bgTextEmb;

    /** 配饰描述文本向量（1024维） */
    private PGvector accessoriesTextEmb;

    /** 发型描述文本向量（1024维） */
    private PGvector hairstyleTextEmb;

    /** 姿势描述文本向量（1024维） */
    private PGvector poseTextEmb;

    /** 背景分类文本向量（1024维，用于聚类或检索） */
    private PGvector sortBgTextEmb;

    /** 表情分类文本向量（1024维，用于聚类或检索） */
    private PGvector sortFacialExpressionTextEmb;

    /** 配饰分类文本向量（1024维，用于聚类或检索） */
    private PGvector sortAccessoriesTextEmb;

    /** 姿势分类文本向量（1024维，用于聚类或检索） */
    private PGvector sortPoseTextEmb;

    /** 扩展信息，JSON格式存储非结构化附加数据 */
    private String extInfo;

    /** 记录创建时间，默认为当前时间戳 */
    private Date createTime;

    /** 记录最后修改时间，默认为当前时间戳 */
    private Date modifyTime;

    /** imageType */
    private String imageType;

    /** clothGenderType */
    private String clothGenderType;

    /**
     * genre，服装类别
     */
    private String genre;

    /**
     * ageGroup，年龄段
     */
    private String ageGroup;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}