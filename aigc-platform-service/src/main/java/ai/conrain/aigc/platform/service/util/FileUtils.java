package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import com.drew.imaging.ImageMetadataReader;
import com.drew.metadata.Directory;
import com.drew.metadata.exif.ExifIFD0Directory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
public abstract class FileUtils {

    private static final Map<String, String> mimeTypeToExtensionMap = new HashMap<>();

    static {
        mimeTypeToExtensionMap.put("image/jpeg", ".jpg");
        mimeTypeToExtensionMap.put("image/png", ".png");
        mimeTypeToExtensionMap.put("image/gif", ".gif");
        mimeTypeToExtensionMap.put("audio/mpeg", ".mp3");
        mimeTypeToExtensionMap.put("audio/wav", ".wav");
    }

    public static String getExtensionFromContentType(String contentType) {
        return mimeTypeToExtensionMap.getOrDefault(contentType, "");
    }

    public static String getExtension(MultipartFile file) {
        String ext = FileUtils.getExtensionFromContentType(file.getContentType());
        if (StringUtils.isBlank(ext)) {
            ext = FilenameUtils.getExtension(file.getOriginalFilename());
            if (ext != null && ext.contains("?")) {
                // 如果包含问号，则截取问号之前的部分
                ext = ext.substring(0, ext.indexOf("?"));
            }
            ext = "." + ext;
        }
        return StringUtils.lowerCase(ext);
    }

    /**
     * 四维合并蒙层到目标图片，将白底mask中为黑色部分设置到图片的alpha通道
     *
     * @param image 原始图片
     * @param mask  蒙层
     * @return 合并后图片
     */
    public static BufferedImage combine4DMask(BufferedImage image, BufferedImage mask) {

        int width = image.getWidth();
        int height = image.getHeight();

        if (width != mask.getWidth() || height != mask.getHeight()) {
            log.warn("Image and mask dimensions do not match. image={}:{},mask={}:{}", width, height, mask.getWidth(),
                    mask.getHeight());
            mask = resize(mask, width, height);
        }

        BufferedImage mergedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int originalRGB = image.getRGB(x, y);
                int maskRGB = mask.getRGB(x, y);

                int red = (originalRGB >> 16) & 0xFF;
                int green = (originalRGB >> 8) & 0xFF;
                int blue = originalRGB & 0xFF;
                int alpha = (maskRGB == 0xFF000000) ? 0 : 255;  // Black in mask means alpha = 0

                int argb = (alpha << 24) | (red << 16) | (green << 8) | blue;
                mergedImage.setRGB(x, y, argb);
            }
        }

        return mergedImage;
    }

    /**
     * 为mask添加alpha通道
     *
     * @param mask 蒙层
     * @return 合并后图片
     */
    public static BufferedImage addAlphaToMaskImageToMask(BufferedImage mask) {

        int width = mask.getWidth();
        int height = mask.getHeight();

        BufferedImage maskImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int maskRGB = mask.getRGB(x, y);

                int red = (maskRGB >> 16) & 0xFF;
                int green = (maskRGB >> 8) & 0xFF;
                int blue = maskRGB & 0xFF;
                int alpha = (maskRGB == 0xFF000000) ? 0 : 255; // Black in mask means alpha = 0

                int argb = (alpha << 24) | (red << 16) | (green << 8) | blue;
                maskImage.setRGB(x, y, argb);
            }
        }

        return maskImage;
    }

    /**
     * 将RGBA图片分离成alpha通道图片和RGB通道图片
     *
     * @param image             RGBA原始图片
     * @param isWhiteBackground true表示高透明度区域为白色，低透明度区域为黑色；false则相反
     * @return 返回包含两张图片的数组：[0]为alpha通道图片，[1]为RGB通道图片
     */
    public static BufferedImage[] separateRgbaImage(BufferedImage image, boolean isWhiteBackground) {
        int width = image.getWidth();
        int height = image.getHeight();

        // 创建alpha通道图片（黑白图）
        BufferedImage alphaImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        // 创建RGB通道图片
        BufferedImage rgbImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

        // 设置背景色
        int bgRed = isWhiteBackground ? 255 : 0;
        int bgGreen = isWhiteBackground ? 255 : 0;
        int bgBlue = isWhiteBackground ? 255 : 0;

        // 设置透明度阈值（0-255），高于此值视为高透明度
        int alphaThreshold = 128;

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                // 获取图片的ARGB值
                int argb = image.getRGB(x, y);

                // 提取各个通道的值
                int alpha = (argb >> 24) & 0xFF;
                int red = (argb >> 16) & 0xFF;
                int green = (argb >> 8) & 0xFF;
                int blue = argb & 0xFF;

                // 处理alpha通道图片（根据透明度阈值和isWhiteBackground设置黑白值）
                boolean isBackground = alpha == alphaThreshold;
                // 如果isWhiteBackground为true，则高透明度为白色；否则高透明度为黑色
                int alphaPixelValue = isWhiteBackground ? (isBackground ? 255 : 0) : (isBackground ? 0 : 255);
                int alphaPixel = (alphaPixelValue << 16) | (alphaPixelValue << 8) | alphaPixelValue;
                alphaImage.setRGB(x, y, alphaPixel);

                // 处理RGB图片（使用背景色进行混合）
                float alphaRatio = alpha / 255.0f;
                int newRed = (int) (red * alphaRatio + bgRed * (1 - alphaRatio));
                int newGreen = (int) (green * alphaRatio + bgGreen * (1 - alphaRatio));
                int newBlue = (int) (blue * alphaRatio + bgBlue * (1 - alphaRatio));

                // 将RGB值转换为ARGB值  
                int rgbPixel = (0xFF << 24) | (newRed << 16) | (newGreen << 8) | newBlue;

                // 将ARGB值设置到RGB图片中  
                rgbImage.setRGB(x, y, rgbPixel);
            }
        }

        // 返回包含两张图片的数组：[0]为alpha通道图片，[1]为RGB通道图片
        return new BufferedImage[]{alphaImage, rgbImage};
    }

    /**
     * 重置图片尺寸
     *
     * @param originalImage 原始图片
     * @param targetWidth   目标宽度
     * @param targetHeight  目标高度
     * @return 图片尺寸
     */
    public static BufferedImage resize(BufferedImage originalImage, int targetWidth, int targetHeight) {
        BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, originalImage.getType());
        Graphics2D graphics2D = resizedImage.createGraphics();

        // 绘制原始图像到新的尺寸上
        graphics2D.drawImage(originalImage.getScaledInstance(targetWidth, targetHeight, Image.SCALE_SMOOTH), 0, 0,
                targetWidth, targetHeight, null);

        // 清理
        graphics2D.dispose();

        return resizedImage;
    }

    /**
     * 将PNG图片转换为JPG图片
     *
     * @param originalImage 原始图片
     * @return jpg图片
     */
    public static BufferedImage convertPngToJpg(BufferedImage originalImage) {
        // 创建目标图片，设置为 RGB 类型，去除透明度
        BufferedImage jpgImage = new BufferedImage(originalImage.getWidth(), originalImage.getHeight(),
                BufferedImage.TYPE_INT_RGB);

        // 创建 Graphics2D 对象，将原始图片绘制到新图片上
        Graphics2D g2d = jpgImage.createGraphics();
        // 设置白色背景以替换透明部分
        g2d.setColor(java.awt.Color.WHITE);
        g2d.fillRect(0, 0, jpgImage.getWidth(), jpgImage.getHeight());
        // 将原始图片绘制到新图片上
        g2d.drawImage(originalImage, 0, 0, null);
        g2d.dispose(); // 释放资源

        return jpgImage;
    }

    /**
     * 纠正图片朝向
     *
     * @param originImage 原始图片
     * @param originFile  原始文件
     * @return 纠正朝向后的图片
     * @throws IOException 异常
     */
    public static BufferedImage correctOrientation(BufferedImage originImage, File originFile) throws IOException {
        int angle = getAngle(originFile);
        if (angle > 0) {
            //纠正方向
            return rotateImage(originImage, angle);
        }

        return originImage;
    }

    /**
     * 增加图片高度，以color的色值作为填充
     *
     * @param originalImage    原始图片
     * @param additionalHeight 增加的高度（像素点）
     * @param color            填充颜色，不传默认白色
     * @param formatName       图片格式
     * @return inputStream流
     */
    public static InputStream increaseImageHeight(BufferedImage originalImage, int additionalHeight, Color color,
                                                  String formatName) throws IOException {
        BufferedImage newImage = increaseImageHeight(originalImage, additionalHeight, color);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(newImage, formatName, baos);
        baos.flush();
        byte[] imageInByte = baos.toByteArray();
        baos.close();

        return new ByteArrayInputStream(imageInByte);
    }

    /**
     * 增加图片高度，以color的色值作为填充
     *
     * @param originalImage    原始图片
     * @param additionalHeight 增加的高度（像素点）
     * @param color            填充颜色，不传默认白色
     * @return 新的图片
     */
    public static BufferedImage increaseImageHeight(BufferedImage originalImage, int additionalHeight, Color color) {
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();

        // 创建一个新的 BufferedImage，增加高度
        BufferedImage newImage = new BufferedImage(originalWidth, originalHeight + additionalHeight,
                BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = newImage.createGraphics();

        // 填充背景为白色
        if (color == null) {
            color = Color.WHITE;
        }

        g2d.setColor(color);
        g2d.fillRect(0, 0, originalWidth, originalHeight + additionalHeight);

        // 将原始图片绘制到新的 BufferedImage 中
        g2d.drawImage(originalImage, 0, 0, null);
        g2d.dispose();

        return newImage;
    }

    /**
     * 混淆文件名称
     *
     * @param fileName 文件名称
     * @return 混淆后的文件名称
     */
    public static String confoundFileName(String fileName) {
        return confoundFileName(fileName, null);
    }

    /**
     * 混淆文件名称
     *
     * @param fileName 文件名称
     * @param tag      标签
     * @return 混淆后的文件名称
     */
    public static String confoundFileName(String fileName, String tag) {
        String[] split = fileName.split("\\.");
        String fileExt = split.length > 1 ? "." + split[1] : "";
        String tags = StringUtils.isBlank(tag) ? "" : tag + "_";
        return split[0] + "_" + tags + RandomStringUtils.randomAlphabetic(5) + fileExt;
    }

    /**
     * 混淆文件名称
     *
     * @param fileName 文件名称
     * @return 混淆后的文件名称
     */
    public static String getFileNameExt(String fileName) {
        String[] split = fileName.split("\\.");
        return split.length > 1 ? "." + split[1] : ".jpg";
    }

    /**
     * 从ossurl中获取原始图片名称
     *
     * @param ossImageUrl 图片的oss地址
     * @return 混淆后的文件名称
     */
    public static String ossImageUrlToFileName(String ossImageUrl) {
        //https://aigc-platform-dev.oss-cn-zhangjiakou.aliyuncs.com/202408/100007/product_92305_1723170828_4967248_0_RtYIb.png?Expires=3299970833&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49Rh&Signature=yYMJGqpAZLAVemkRnGHcf00Si%2Fo%3D
        String fileName = StringUtils.substringAfterLast(ossImageUrl, "/");
        fileName = StringUtils.substringBefore(fileName, "?");

        //去除混淆的图片名称
        String prefix = StringUtils.substringBeforeLast(fileName, "_");
        String suffix = StringUtils.substringAfterLast(fileName, ".");

        return prefix + "." + suffix;
    }

    /**
     * 从ossurl中获取原始完整图片名称
     *
     * @param ossImageUrl 图片的oss地址
     * @return 混淆后的文件名称
     */
    public static String ossImageUrlToFullFileName(String ossImageUrl) {
        //https://aigc-platform-dev.oss-cn-zhangjiakou.aliyuncs.com/202408/100007/product_92305_1723170828_4967248_0_RtYIb.png?Expires=3299970833&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49Rh&Signature=yYMJGqpAZLAVemkRnGHcf00Si%2Fo%3D
        String fileName = StringUtils.substringAfterLast(ossImageUrl, "/");
        fileName = StringUtils.substringBefore(fileName, "?");

        //去除混淆的图片名称
        String prefix = StringUtils.substringBeforeLast(fileName, ".");
        String suffix = StringUtils.substringAfterLast(fileName, ".");

        return prefix + "." + suffix;
    }

    /**
     * 将文件名称列表压缩为zip文件
     *
     * @param fileNames 文件名称列表
     * @return zip文件的二进制流
     * @throws IOException 异常
     */
    public static byte[] zip(List<String> fileNames) throws IOException {
        // 创建输出流
        try (ByteArrayOutputStream outStream = new ByteArrayOutputStream(); ZipOutputStream zos = new ZipOutputStream(
                outStream)) {

            // 添加文件到ZIP
            for (String fileName : fileNames) {
                Path path = Paths.get(fileName);
                if (Files.exists(path)) {
                    // 将文件添加为新的ZIP条目
                    zos.putNextEntry(new ZipEntry(path.getFileName().toString()));

                    // 将文件内容写入ZIP条目
                    Files.copy(path, zos);
                    zos.closeEntry();
                }
            }
            // 关闭ZIP流，确保所有数据写入完成
            zos.finish();

            // 返回ZIP数据的字节数组
            return outStream.toByteArray();
        }
    }

    /**
     * 从image的名称中解析出任务id
     *
     * @param imageName 图片名称
     * @return taskId
     */
    public static Integer getTaskIdByImageName(String imageName) {
        if (StringUtils.isBlank(imageName)) {
            return null;
        }
        String fileName = imageName.substring(imageName.lastIndexOf('/') + 1);
        String[] split = fileName.split("_");

        return split.length > 1 && StringUtils.isNumeric(split[1]) ? Integer.valueOf(split[1]) : null;
    }

    /**
     * 获取图片旋转角度
     *
     * @param originFile 原始文件
     * @return 旋转角度
     */
    private static int getAngle(File originFile) {
        // 这里假设你有一个方法可以从BufferedImage中提取出文件路径或者输入流以读取EXIF信息
        try {
            for (Directory directory : ImageMetadataReader.readMetadata(originFile).getDirectories()) {
                if (directory instanceof ExifIFD0Directory) {
                    ExifIFD0Directory exif = (ExifIFD0Directory) directory;
                    int orientation = exif.getInt(ExifIFD0Directory.TAG_ORIENTATION);
                    switch (orientation) {
                        case 6: // 顺时针旋转 90 度
                            return 90;
                        case 3: // 顺时针旋转 180 度
                            return 180;
                        case 8: // 顺时针旋转 270 度
                            return 270;
                        default: // 无需旋转
                            return 0;
                    }
                }
            }
        } catch (Exception e) {
            log.error("矫正图片方向异常", e);
        }
        return 0;
    }

    /**
     * 旋转图片
     *
     * @param image 图片
     * @param angle 角度
     * @return 旋转后图片
     */
    private static BufferedImage rotateImage(BufferedImage image, int angle) {
        double radians = Math.toRadians(angle);
        double sin = Math.abs(Math.sin(radians)), cos = Math.abs(Math.cos(radians));
        int w = image.getWidth(), h = image.getHeight();
        int neww = (int) Math.floor(w * cos + h * sin), newh = (int) Math.floor(h * cos + w * sin);

        BufferedImage rotatedImage = new BufferedImage(neww, newh, image.getType());
        Graphics2D g = rotatedImage.createGraphics();
        g.translate((neww - w) / 2, (newh - h) / 2);
        g.rotate(radians, w / 2, h / 2);
        g.drawRenderedImage(image, null);
        g.dispose();

        return rotatedImage;
    }

    /**
     * 处理RGBA图片并上传到OSS，分离成alpha通道和RGB通道两张图片
     *
     * @param file               原始图片文件
     * @param fileNameWithoutExt 不带扩展名的文件名
     * @param ossService         OSS服务
     * @return 包含mask和rgb图片OSS地址的数组：[0]为mask图片地址，[1]为rgb图片地址
     * @throws IOException IO异常
     */
    public static String[] processAndUploadRgbaImage(File file, String fileNameWithoutExt, OssService ossService)
            throws IOException {
        // 读取图片
        BufferedImage originalImage = ImageIO.read(file);
        if (originalImage == null) {
            log.error("Failed to read image file: " + file.getPath());
            return null;
        }

        // 分离图片通道
        BufferedImage[] bufferedImages = separateRgbaImage(originalImage, false);

        // 创建输出流
        try (ByteArrayOutputStream alphaStream = new ByteArrayOutputStream();
             ByteArrayOutputStream rgbStream = new ByteArrayOutputStream()) {

            // 将图片写入输出流
            ImageIO.write(bufferedImages[0], "png", alphaStream);
            ImageIO.write(bufferedImages[1], "png", rgbStream);

            // 获取图片字节数组
            byte[] alphaImageBytes = alphaStream.toByteArray();
            byte[] rgbImageBytes = rgbStream.toByteArray();

            // 上传alpha图片
            String maskOssUrl;
            try (ByteArrayInputStream alphaImageByteArrayInputStream = new ByteArrayInputStream(alphaImageBytes)) {
                maskOssUrl = ossService.upload(fileNameWithoutExt + "_mask.png", alphaImageByteArrayInputStream);
            }

            // 上传rgb图片
            String rgbOssUrl;
            try (ByteArrayInputStream rgbImageByteArrayInputStream = new ByteArrayInputStream(rgbImageBytes)) {
                rgbOssUrl = ossService.upload(fileNameWithoutExt + ".png", rgbImageByteArrayInputStream);
            }

            return new String[]{maskOssUrl, rgbOssUrl};
        }
    }

    /**
     * 将图片转换为PNG格式
     *
     * @param image 原始图片
     * @return PNG格式的图片
     */
    public static BufferedImage convertToPng(BufferedImage image) {
        // 检查图片是否已经是PNG格式（TYPE_INT_ARGB 或 TYPE_4BYTE_ARGB）
        if (image.getType() == BufferedImage.TYPE_INT_ARGB || image.getType() == BufferedImage.TYPE_4BYTE_ABGR) {
            return image;
        }

        // 创建新的PNG格式图片
        BufferedImage pngImage = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_ARGB);

        // 绘制原始图片到新图片
        Graphics2D g2d = pngImage.createGraphics();
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();

        return pngImage;
    }
}
