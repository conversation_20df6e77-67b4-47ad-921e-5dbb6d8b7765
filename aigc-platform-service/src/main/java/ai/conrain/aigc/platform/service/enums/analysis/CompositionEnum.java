/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums.analysis;

import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 构图枚举
 *
 * <AUTHOR>
 * @version : CompositionEnum.java, v 0.1 2025/8/24 23:54 renxiao.wu Exp $
 */
@Getter
public enum CompositionEnum {
    LONG_SHOT("Long_shot", "远景", CameraAngleEnum.WHOLE_BODY),
    FULL_BODY("Full_body", "全身", CameraAngleEnum.WHOLE_BODY),
    BUST_SHOT("Bust_shot", "胸部以上", CameraAngleEnum.UPPER_BODY),
    UPPER_BODY_CLOSE_UP("Upper_body_close_up", "上半身特写", CameraAngleEnum.UPPER_BODY),
    LOWER_BODY_CLOSE_UP("Lower_body_close_up", "下半身特写", CameraAngleEnum.LOWER_BODY),
    MEDIUM_SHOT("Medium_shot", "中景（头顶到大腿中段）", CameraAngleEnum.UPPER_BODY),
    NONE("None", "其他", CameraAngleEnum.WHOLE_BODY),
    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    private final CameraAngleEnum cameraAngle;

    private CompositionEnum(String code, String desc, CameraAngleEnum cameraAngle) {
        this.code = code;
        this.desc = desc;
        this.cameraAngle = cameraAngle;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static CompositionEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (CompositionEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code         枚举码
     * @param defaultValue 默认值
     * @return 对应枚举
     */
    public static CompositionEnum getByCode(String code, CompositionEnum defaultValue) {
        if (StringUtils.isBlank(code)) {
            return defaultValue;
        }

        for (CompositionEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return defaultValue;
    }
}
