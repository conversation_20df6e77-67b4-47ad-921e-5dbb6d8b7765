package ai.conrain.aigc.platform.service.util;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.plugins.jpeg.JPEGImageWriteParam;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Iterator;


/**
 * 图片处理工具类
 */
public class ImageUtils {

    /**
     * 全质量JPG保存方法
     *
     * @param image      图片
     * @param outputFile 输出文件
     * @throws IOException IO异常
     */
    public static void saveFullQualityJPG(BufferedImage image, File outputFile) throws IOException {
        saveHighQualityJPG(image, outputFile, 1);
    }

    /**
     * 高质量JPG保存方法
     *
     * @param image      图片
     * @param outputFile 输出文件
     * @param quality    质量参数：0.0-1.0，越高质量越好
     * @throws IOException IO异常
     */
    public static void saveHighQualityJPG(BufferedImage image, File outputFile, float quality) throws IOException {
        // 获取JPG编码器
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpg");
        if (!writers.hasNext()) {
            throw new IllegalStateException("No JPG writer found");
        }

        ImageWriter writer = writers.next();

        // 设置JPG写入参数
        JPEGImageWriteParam jpegParams = new JPEGImageWriteParam(null);
        jpegParams.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        jpegParams.setCompressionQuality(quality); // 质量参数：0.0-1.0，越高质量越好

        // 创建输出流
        try (FileOutputStream fos = new FileOutputStream(outputFile);
             ImageOutputStream ios = ImageIO.createImageOutputStream(fos)) {

            writer.setOutput(ios);
            writer.write(null, new IIOImage(image, null, null), jpegParams);

        } finally {
            writer.dispose();
        }
    }

    /**
     * 全质量JPG保存方法
     *
     * @param image        图片
     * @param outputStream 输出流
     * @throws IOException IO异常
     */
    public static void saveFullQualityJPG(BufferedImage image, OutputStream outputStream) throws IOException {
        saveHighQualityJPG(image, outputStream, 1);
    }


    /**
     * 高质量JPG保存方法（支持OutputStream）
     *
     * @param image        图片
     * @param outputStream 输出流
     * @param quality      质量参数：0.0-1.0，越高质量越好
     * @throws IOException IO异常
     */
    public static void saveHighQualityJPG(BufferedImage image, OutputStream outputStream, float quality) throws IOException {
        // 获取JPG编码器
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpg");
        if (!writers.hasNext()) {
            throw new IllegalStateException("No JPG writer found");
        }

        ImageWriter writer = writers.next();

        // 设置JPG写入参数
        JPEGImageWriteParam jpegParams = new JPEGImageWriteParam(null);
        jpegParams.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        jpegParams.setCompressionQuality(quality); // 质量参数：0.0-1.0，越高质量越好

        // 创建输出流
        try (ImageOutputStream ios = ImageIO.createImageOutputStream(outputStream)) {
            writer.setOutput(ios);
            writer.write(null, new IIOImage(image, null, null), jpegParams);
        } finally {
            writer.dispose();
        }
    }

}
