package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.ImageQuery;
import ai.conrain.aigc.platform.service.model.vo.ClothInfoVO;
import ai.conrain.aigc.platform.service.model.vo.ImageUploadReq;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * 图像基本信息 Service定义
 *
 * <AUTHOR>
 * @version ImageService.java v 0.1 2025-07-30 02:33:24
 */
public interface ImageService {
	
	/**
	 * 查询图像基本信息对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ImageVO selectById(Integer id);

	/**
	 * 删除图像基本信息对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加图像基本信息对象
	 * @param image 对象参数
	 * @return 返回结果
	 */
	ImageVO insert(ImageVO image);

    /**
     * 更新服装类型
     *
     * @param image
     */
    void updateClothType(ImageQuery image);

	/**
	 * 修改图像基本信息对象
	 * @param image 对象参数
	 */
	void updateByIdSelective(ImageVO image);

	/**
	 * 带条件批量查询图像基本信息列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<ImageVO> queryImageList(ImageQuery query);

    /**
     * 查询图像标签
     *
     * @param query
     * @return
     */
    List<String> queryImageTags(ImageQuery query);

    /**
	 * 带条件查询图像基本信息数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryImageCount(ImageQuery query);

	/**
	 * 带条件分页查询图像基本信息
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<ImageVO> queryImageByPage(ImageQuery query);

    /**
     * 带条件查询总数
     *
     * @param query
     * @return
     */
    Map<String, Long> queryBatchCount(ImageQuery query);

    /**
     * 根据url创建或者获取id
     *
     * @param clothInfo
     */
    Integer saveClothInfo(ClothInfoVO clothInfo);

    /**
     * 更新图片的md5
     */
    void updateImageHash(ImageQuery query);

    /**
     * 上传图片
     *
     * @param req
     * @return
     */
    ImageVO upload(ImageUploadReq req);

    /**
     * 统计 type 为 scene 的图片中各个 intendedUse 流派的数量
     * @return 流派统计结果，key为流派名称，value为数量
     */
    Map<String, Integer> countByIntendedUse();

    List<ImageVO> selectUncaptionedSceneImages4Embedding(Integer limit);

    List<ImageVO> selectGeminiFlashCaptionedSceneImages4Embedding(Integer limit);
}