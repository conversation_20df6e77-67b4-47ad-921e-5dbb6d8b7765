package ai.conrain.aigc.platform.service.model.biz.agent;

import lombok.Data;

import java.io.Serializable;

/**
 * 多样性配置选项
 * 用于封装MMR（Maximal Marginal Relevance）相关参数
 */
@Data
public class DiversifyOptions implements Serializable {

    // MMR参数，用于平衡相关性和多样性，取值范围[0,1]
    // 0表示完全关注多样性，1表示完全关注相关性
    private Double mmrLambda = 0.5;

    // MMR窗口大小，表示在多少个候选项中进行多样性选择
    private Integer mmrWindowSize = 10;
}