package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.example.ImageCaptionExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import ai.conrain.aigc.platform.integration.ai.imageAnalysis.ImageAnalysisService;
import ai.conrain.aigc.platform.integration.ai.imageAnalysis.ImageQualityApiService;
import ai.conrain.aigc.platform.integration.ai.imageAnalysis.SortModelEmbeddingService;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisResult;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisTaskResponse;
import ai.conrain.aigc.platform.integration.ai.model.StyleImageEmbeddingResult;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.embedding.ComposedEmbeddingService;
import ai.conrain.aigc.platform.service.component.CaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.component.ImageCaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.agent.AgentUtil;
import ai.conrain.aigc.platform.service.component.onnx.GenreRecognizeOnnxService;
import ai.conrain.aigc.platform.service.enums.AgeGroupEnum;
import ai.conrain.aigc.platform.service.enums.ClothGenderEnum;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageCandidate;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ImageCaptionConverter;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionQuery;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionUserQuery;
import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionUserVO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.MapUtil;
import ai.conrain.aigc.platform.service.util.VectorUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pgvector.PGvector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * ImageCaptionService实现
 *
 * <AUTHOR>
 * @version ImageCaptionService.java v 0.1 2025-07-25 11:33:21
 */
@Slf4j
@Service
public class ImageCaptionServiceImpl implements ImageCaptionService {

    /** DAO */
    @Autowired
    private ImageCaptionDAO imageCaptionDAO;

    @Autowired
    private ComposedEmbeddingService embeddingService;

    /** 内存缓存：存储所有ImageCaption数据 */
    private final Map<Integer, ImageCaptionVO> imageCaptionCache = new ConcurrentHashMap<>();

    /** 缓存初始化状态标识 */
    private final AtomicBoolean cacheInitialized = new AtomicBoolean(false);

    /** 缓存加载状态标识 */
    private final AtomicBoolean cacheLoading = new AtomicBoolean(false);

    private static final int TEXT_EMB_DIMENSION = 256;

    @Autowired
    private ImageService imageService;

    @Autowired
    private SortModelEmbeddingService sortModelEmbeddingService;

    @Autowired
    private ImageCaptionUserService imageCaptionUserService;

    @Autowired
    private CaptionUserService captionUserService;

    @Autowired
    private ImageAnalysisService imageAnalysisService;

    @Autowired
    private TairService tairService;

    @Autowired
    private ImageQualityApiService imageQualityApiService;

    @Autowired
    private GenreRecognizeOnnxService genreRecognizeOnnxService;

    @Override
    public ImageCaptionVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        ImageCaptionDO data = imageCaptionDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return ImageCaptionConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = imageCaptionDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ImageCaption失败");
    }

    @Override
    public ImageCaptionVO insert(ImageCaptionVO imageCaption) {
        AssertUtil.assertNotNull(imageCaption, ResultCode.PARAM_INVALID, "imageCaption is null");
        AssertUtil.assertTrue(imageCaption.getId() == null, ResultCode.PARAM_INVALID, "imageCaption.id is present");

        // 创建时间、修改时间兜底
        if (imageCaption.getCreateTime() == null) {
            imageCaption.setCreateTime(new Date());
        }

        if (imageCaption.getModifyTime() == null) {
            imageCaption.setModifyTime(new Date());
        }

        ImageCaptionDO data = ImageCaptionConverter.vo2DO(imageCaption);
        // 逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        Integer n = imageCaptionDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ImageCaption失败");
        AssertUtil.assertNotNull(data.getId(), "新建ImageCaption返回id为空");
        imageCaption.setId(data.getId());
        return imageCaption;
    }

    @Override
    public void updateByIdSelective(ImageCaptionVO imageCaption) {
        AssertUtil.assertNotNull(imageCaption, ResultCode.PARAM_INVALID, "imageCaption is null");
        AssertUtil.assertTrue(imageCaption.getId() != null, ResultCode.PARAM_INVALID, "imageCaption.id is null");

        // 使用反射验证所有PGvector类型的字段
        AssertUtil.validateDimensionOfNonNullPGVectorFields(imageCaption);

        // 修改时间必须更新
        imageCaption.setModifyTime(new Date());
        ImageCaptionDO data = ImageCaptionConverter.vo2DO(imageCaption);
        // 逻辑删除标过滤
        data.setDeleted(false);
        int n = imageCaptionDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ImageCaption失败，影响行数:" + n);
    }

    @Override
    public List<ImageCaptionVO> queryImageCaptionList(ImageCaptionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageCaptionExample example = ImageCaptionConverter.query2Example(query);

        List<ImageCaptionDO> list = imageCaptionDAO.selectByExample(example);
        return ImageCaptionConverter.doList2VOList(list);
    }

    @Override
    public List<ImageCaptionVO> queryImageCaptionList4Recall(ImageCaptionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageCaptionExample example = ImageCaptionConverter.query2Example(query);

        List<ImageCaptionDO> list = imageCaptionDAO.selectRecallVectorsByExample(example);
        return ImageCaptionConverter.doList2VOList(list);
    }

    @Override
    public Long queryImageCaptionCount(ImageCaptionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageCaptionExample example = ImageCaptionConverter.query2Example(query);
        return imageCaptionDAO.countByExample(example);
    }

    /**
     * 带条件分页查询图像标注
     */
    @Override
    public PageInfo<ImageCaptionVO> queryImageCaptionByPage(ImageCaptionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
                "pageNum or pageSize is invalid,pageNum:"
                        + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<ImageCaptionVO> page = new PageInfo<>();

        ImageCaptionExample example = ImageCaptionConverter.query2Example(query);
        long totalCount = imageCaptionDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<ImageCaptionDO> list = imageCaptionDAO.selectByExample(example);
        page.setList(ImageCaptionConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    /**
     * 根据款式向量相似度查询图像打标
     */
    @Override
    public List<ImageCaptionVO> queryByStyleVectorSimilarity(PGvector styleVector, ClothGenderEnum gender,
            double similarityThreshold, int limit, String genre, AgeGroupEnum ageGroup,
            Set<Integer> excludeImageCaptionIds) {

        AssertUtil.assertNotNull(styleVector, ResultCode.PARAM_INVALID, "styleVector is null");
        AssertUtil.assertNotNull(gender, ResultCode.PARAM_INVALID, "gender is null or blank");
        AssertUtil.assertNotNull(ageGroup, ResultCode.PARAM_INVALID, "ageGroup is null or blank");
        AssertUtil.assertTrue(similarityThreshold >= 0 && similarityThreshold <= 1,
                ResultCode.PARAM_INVALID, "similarityThreshold must be between 0 and 1");
        AssertUtil.assertTrue(limit > 0, ResultCode.PARAM_INVALID, "limit must be greater than 0");

        List<ImageCaptionDO> matchedImages = imageCaptionDAO.selectByStyleVector(styleVector, gender.getCode(),
                similarityThreshold, limit, genre, ageGroup.getCode(), excludeImageCaptionIds);

        log.info(
                "queryByStyleVectorSimilarity,gender:{},similarity:{},limit:{},genre:{}, excludeIds size:{}, matchedImages size:{}",
                gender.getCode(), similarityThreshold, limit, genre,
                excludeImageCaptionIds != null ? excludeImageCaptionIds.size() : 0, matchedImages.size());

        return ImageCaptionConverter.doList2VOList(matchedImages);
    }

    @Override
    public PGvector calcClothTextVector(ImageAnalysisCaption clothAnalysis, ClothTypeEnum clothType) {
        String clothFeatureText = this.getClothFeatureTextByAnalysis(clothAnalysis, clothType);
        AssertUtil.assertNotBlank(clothFeatureText, ResultCode.PARAM_INVALID, "clothFeatureText is null or blank");
        return embeddingService.getTextEmbeddingByMultiModalModel(clothFeatureText);
    }

    /**
     * 计算打标向量
     *
     * @param imageCaptionVO
     * @param skipSortEmbedding
     */
    @Override
    public void initEmbeddingsByCaption(ImageCaptionVO imageCaptionVO, ClothTypeEnum clothTypeEnum, boolean skipSortEmbedding) {
        AssertUtil.assertNotNull(imageCaptionVO, "imageCaptionVO is null");
        AssertUtil.assertNotNull(imageCaptionVO.getCaption(), "imageCaptionVO.getCaption() is null");
        AssertUtil.assertNotNull(imageCaptionVO.getImageId(), "imageCaptionVO.getImageId() is null");

        ImageVO imageVO = imageService.selectById(imageCaptionVO.getImageId());
        AssertUtil.assertNotNull(imageVO, "imageVO is null");
        AssertUtil.assertNotBlank(imageVO.getUrl(), "imageVO.getUrl() is null");

        ImageAnalysisCaption caption = imageCaptionVO.getCaption();

        // 性别
        if (StringUtils.isBlank(imageCaptionVO.getClothGenderType())) {
            if (caption.getModel() != null && StringUtils.isNotBlank(caption.getModel().getGender())) {
                ClothGenderEnum genderEnum = ClothGenderEnum.getByCode(caption.getModel().getGender());
                if (genderEnum != null) {
                    imageCaptionVO.setClothGenderType(genderEnum.getCode());
                }
            }
        }

        // 年龄段
        if (StringUtils.isBlank(imageCaptionVO.getAgeGroup())) {
            if (caption.getModel() != null && StringUtils.isNotBlank(caption.getModel().getAge())) {
                String captionAge = caption.getModel().getAge();
                imageCaptionVO.setAgeGroup(AgentUtil.getAgeGroupEnum(captionAge).getCode());
            }
        }

        // image type
        imageCaptionVO.setImageType(imageVO.getType());

        // 流派
        imageCaptionVO.setGenre(genreRecognizeOnnxService.recognizeGenreFromUrl(imageVO.getUrl()));

        getSortEmbeddings(imageCaptionVO, clothTypeEnum, skipSortEmbedding, imageVO, caption);

        // 批量计算文本向量以提高效率
        // 使用LinkedHashMap保持插入顺序，key为文本内容，value为对应的向量设置方法
        Map<String, Consumer<PGvector>> textEmbeddingMap = new LinkedHashMap<>();

        // 收集需要向量化的文本及其对应的设置方法
        // 款式向量
        if (caption.getClothing() != null) {
            String styleText = this.getClothStyleDescription(caption);
            if (StringUtils.isNotBlank(styleText)) {
                textEmbeddingMap.put(styleText, imageCaptionVO::setClothStyleTextEmb);
            }
        }

        // 背景道具文本向量
        if (caption.getShootingTheme() != null
                && StringUtils.isNotBlank(caption.getShootingTheme().getShootingScene())) {
            textEmbeddingMap.put(caption.getShootingTheme().getShootingScene(), imageCaptionVO::setBgTextEmb);
        }

        // 配饰搭配文本向量
        if (caption.getClothing() != null && StringUtils.isNotBlank(caption.getClothing().getAccessories())) {
            textEmbeddingMap.put(caption.getClothing().getAccessories(), imageCaptionVO::setAccessoriesTextEmb);
        }

        // 发型文本向量
        if (caption.getModel() != null && StringUtils.isNotBlank(caption.getModel().getHairstyle())) {
            textEmbeddingMap.put(caption.getModel().getHairstyle(), imageCaptionVO::setHairstyleTextEmb);
        }

        // 姿势文本向量
        if (caption.getModel() != null && StringUtils.isNotBlank(caption.getModel().getPosture())) {
            textEmbeddingMap.put(caption.getModel().getPosture(), imageCaptionVO::setPoseTextEmb);
        }

        // 批量调用阿里云服务纯文本的计算向量api，非多模态api，用于服装匹配召回使用，256维
        if (!textEmbeddingMap.isEmpty()) {
            List<String> textsToEmbed = new ArrayList<>(textEmbeddingMap.keySet());
            List<PGvector> embeddings = embeddingService.getEmbeddingByTexts(textsToEmbed, TEXT_EMB_DIMENSION);

            // 根据顺序设置对应的向量
            List<Consumer<PGvector>> setters = new ArrayList<>(textEmbeddingMap.values());
            for (int i = 0; i < setters.size() && i < embeddings.size(); i++) {
                setters.get(i).accept(embeddings.get(i));
            }
        }

        this.updateByIdSelective(imageCaptionVO);
    }

    @Override
    public void initSortEmbeddings(ImageCaptionVO imageCaption) {
        log.info("[ImageCaptionService] initSortEmbeddings: {}", imageCaption.getId());
        ImageVO imageVO = imageService.selectById(imageCaption.getImageId());
        this.getSortEmbeddings(imageCaption, ClothTypeEnum.TwoPiece, false, imageVO, imageCaption.getCaption());

        this.updateByIdSelective(imageCaption);

        log.info("[ImageCaptionService] initSortEmbeddings done: {}", imageCaption.getId());
    }

    private void getSortEmbeddings(ImageCaptionVO imageCaptionVO, ClothTypeEnum clothTypeEnum, boolean skipSortEmbedding, ImageVO imageVO, ImageAnalysisCaption caption) {
        // 图像向量（组），由阿九提供的api，计算8个向量，用于排序模型的输入
        if (!skipSortEmbedding) {
            String sortEmbTaskId = String.format("IM_EMB_%d_%s", imageCaptionVO.getId(), System.currentTimeMillis());
            StyleImageEmbeddingResult styleImageEmbeddingResult = sortModelEmbeddingService
                    .calcStyleImageEmbeddings(imageVO.getUrl(), sortEmbTaskId, caption);

            imageCaptionVO.setImgEmb(VectorUtil
                    .createVectorOrZeroIfNull(styleImageEmbeddingResult.getEmbeddings().getOptImageEmbedding(), 1024));
            imageCaptionVO.setBgImgEmb(VectorUtil.createVectorOrZeroIfNull(
                    styleImageEmbeddingResult.getEmbeddings().getBackgroundImageEmbedding(), 1024));
            imageCaptionVO.setModelFacialImgEmb(
                    VectorUtil.createVectorOrZeroIfNull(
                            styleImageEmbeddingResult.getEmbeddings().getExpressionImageEmbedding(), 1024));
            imageCaptionVO.setModelPoseImgEmb(VectorUtil
                    .createVectorOrZeroIfNull(styleImageEmbeddingResult.getEmbeddings().getPoseImageEmbedding(), 1024));
            imageCaptionVO.setSortBgTextEmb(
                    VectorUtil.createVectorOrZeroIfNull(
                            styleImageEmbeddingResult.getEmbeddings().getBackgroundImageEmbedding(), 1024));
            imageCaptionVO.setSortFacialExpressionTextEmb(
                    VectorUtil.createVectorOrZeroIfNull(
                            styleImageEmbeddingResult.getEmbeddings().getExpressionTextEmbedding(), 1024));
            imageCaptionVO.setSortAccessoriesTextEmb(
                    VectorUtil.createVectorOrZeroIfNull(styleImageEmbeddingResult.getEmbeddings().getMatTextEmbedding(),
                            1024));
            imageCaptionVO.setSortPoseTextEmb(VectorUtil
                    .createVectorOrZeroIfNull(styleImageEmbeddingResult.getEmbeddings().getPoseTextEmbedding(), 1024));
        }

        // 服装描述文本向量（使用多模态api计算，且维度为1024，api和维度，需要与阿九保持一致）
        if (caption.getClothing() != null) {
            try {
                PGvector clothVector = this.calcClothTextVector(caption, clothTypeEnum);
                imageCaptionVO.setClothTextEmb(clothVector);
            } catch (Exception e) {
                log.error("计算服装描述文本向量失败", e);
            }
        }
    }

    @Override
    public void fillSortVectors(List<StyleImageCandidate> candidates) {
        AssertUtil.assertNotEmpty(candidates, ResultCode.PARAM_INVALID, "candidates is null");
        List<Integer> ids = candidates.stream().map(c -> c.getImageCaption().getId()).toList();
        AssertUtil.assertNotEmpty(ids, ResultCode.PARAM_INVALID, "candidates caption is empty");

        List<ImageCaptionDO> sortVectors = imageCaptionDAO.selectSortVectors(ids);
        Map<Integer, ImageCaptionDO> sortVectorMap = sortVectors.stream()
                .collect(Collectors.toMap(ImageCaptionDO::getId, c -> c));
        candidates.forEach(c -> {
            ImageCaptionVO cap = c.getImageCaption();
            ImageCaptionDO target = sortVectorMap.get(cap.getId());
            if (target != null) {
                cap.setImgEmb(target.getImgEmb());
                cap.setBgImgEmb(target.getBgImgEmb());
                cap.setModelFacialImgEmb(target.getModelFacialImgEmb());
                cap.setModelPoseImgEmb(target.getModelPoseImgEmb());
                cap.setSortBgTextEmb(target.getSortBgTextEmb());
                cap.setSortFacialExpressionTextEmb(target.getSortFacialExpressionTextEmb());
                cap.setSortAccessoriesTextEmb(target.getSortAccessoriesTextEmb());
                cap.setSortPoseTextEmb(target.getSortPoseTextEmb());
            }

            c.setImageCaption(cap);
        });
    }

    @Override
    public String createImageCaptionTaskWithNotify(ImageVO imageVO) {

        CaptionUserVO geminiFlash = captionUserService.getOrCreateByUsername("gemini-flash");
        List<ImageCaptionUserVO> imageCaptionUserVOS = getGeminiFlashImageCaptionUsers(imageVO, geminiFlash);
        if (CollectionUtils.isNotEmpty(imageCaptionUserVOS)
                && imageCaptionUserVOS.getFirst().getCaption() != null) {
            log.info("[ImageCaptionService] gemini-flash already has a caption for image.id:{}", imageVO.getId());
            return null;
        }

        ImageAnalysisTaskResponse taskWithNotify = imageAnalysisService.createAnalysisTaskWithNotify(imageVO.getUrl(), false);
        if (taskWithNotify != null) {
            String taskId = taskWithNotify.getTaskId();
            AssertUtil.assertNotBlank(taskId, "label taskId is null");

            tairService.setString(taskId, imageVO.getId().toString(), 60 * 60 * 24);

            log.info("createImageCaptionTaskWithNotify cache taskId:{} for image.id:{}", taskId, imageVO.getId());

            return taskId;
        }
        return null;
    }

    @Override
    public void onCaptionSuccess(ImageAnalysisResult imageAnalysisResult) {

        log.info("onCaptionSuccess: imageAnalysisResult:{}", imageAnalysisResult);

        String labelTaskId = imageAnalysisResult.getTaskId();

        String string = tairService.getString(labelTaskId);
        if (StringUtils.isBlank(string)) {
            log.error("onCaptionSuccess: ignore taskId not found in tair, taskId:{}", labelTaskId);
            return;
        }

        Integer imageId = Integer.parseInt(string);
        ImageVO imageVO = imageService.selectById(imageId);


        CaptionUserVO geminiFlash = captionUserService.getOrCreateByUsername("gemini-flash");

        //保存image_caption_user: gemini-flash
        saveImageCaptionUserOfGeminiFlash(imageAnalysisResult, imageVO, geminiFlash);

        //创建image_caption
        createImageCaptionEmbeddings(imageVO, false, false);
    }

    private void saveImageCaptionUserOfGeminiFlash(ImageAnalysisResult imageAnalysisResult, ImageVO imageVO, CaptionUserVO geminiFlash) {
        Map<String, Object> flattenedCaption = MapUtil.flatten(JSONObject.parseObject(imageAnalysisResult.getRaw()));
        ImageCaptionUserVO imageCaptionUserVO = new ImageCaptionUserVO();
        imageCaptionUserVO.setImageId(imageVO.getId());
        imageCaptionUserVO.setUserId(geminiFlash.getId());
        imageCaptionUserVO.setCaption(com.alibaba.fastjson2.JSONObject.from(flattenedCaption));
        imageCaptionUserService.insert(imageCaptionUserVO);
    }

    /**
     * 根据图像信息创建图像打标对象
     *
     * @param imageVO
     * @param initEmbeddings
     * @param ignoreNoCaption
     * @return
     */
    @Override
    public ImageCaptionVO createImageCaptionEmbeddings(ImageVO imageVO, boolean initEmbeddings, boolean ignoreNoCaption) {
        AssertUtil.assertNotNull(imageVO, ResultCode.PARAM_INVALID, "imageVO is null");

        // 首先根据imageId查询是否已存在ImageCaptionVO
        List<ImageCaptionVO> existingCaptions = getExistingCaptions(imageVO);

        ImageCaptionVO targetImageCaptionVO = new ImageCaptionVO();
        if (CollectionUtils.isNotEmpty(existingCaptions)) {
            // 如果已存在，使用现有的ImageCaptionVO进行更新
            targetImageCaptionVO = existingCaptions.getFirst();
            log.info("[ImageCaptionServiceImpl] 找到已存在的ImageCaptionVO, id:{}, imageId:{}", targetImageCaptionVO.getId(), imageVO.getId());
        } else {
            // 如果不存在，创建新的ImageCaptionVO对象
            targetImageCaptionVO.setImageId(imageVO.getId());
            log.info("[ImageCaptionServiceImpl] 创建新的ImageCaptionVO, imageId:{}", imageVO.getId());
        }

        CaptionUserVO geminiFlash = captionUserService.getOrCreateByUsername("gemini-flash");
        List<ImageCaptionUserVO> imageCaptionUserVOS = getGeminiFlashImageCaptionUsers(imageVO, geminiFlash);

        //已经有gemini-flash打标
        if (CollectionUtils.isNotEmpty(imageCaptionUserVOS)
            && imageCaptionUserVOS.getFirst().getCaption() != null) {

            log.info("[ImageCaptionServiceImpl] gemini-flash已打标, caption:{}", imageCaptionUserVOS.getFirst().getCaption());
            targetImageCaptionVO.setCaption(getImageAnalysisCaption(imageCaptionUserVOS));

            //没使用gemini-flash打标
        } else {
            if (ignoreNoCaption) {
                log.info("[ImageCaptionServiceImpl] gemini-flash未打标, ignoreNoCaption:{}，忽略这条记录:{}", true, imageVO.getId());
                return null;
            }

            log.info("[ImageCaptionServiceImpl] gemini-flash未打标");

            ImageAnalysisResult analysisResult = imageAnalysisService.getImageAnalysisResultAndCache2Redis(imageVO.getUrl(), false);
            if (analysisResult == null || analysisResult.getAnalysis() == null) {
                log.error("获取图像分析结果失败，请检查图像分析服务是否正常");
                return null;
            }
            targetImageCaptionVO.setCaption(analysisResult.getAnalysis());

            saveImageCaptionUserOfGeminiFlash(analysisResult, imageVO, geminiFlash);
        }

        if (targetImageCaptionVO.getCaption() == null) {
            log.error("图像分析结果为空，请检查图像分析服务是否正常");
            return null;
        }

        // 获取图像质量分数
        if (targetImageCaptionVO.getQualityScore() == null){
            Double score = imageQualityApiService.classifyImageQuality(imageVO.getUrl());
            if (score != null) {
                targetImageCaptionVO.setQualityScore(score);
            }
        }

        // 保存到数据库（插入或更新）
        if (targetImageCaptionVO.getId() == null) {
            // 新记录，执行插入
            targetImageCaptionVO = this.insert(targetImageCaptionVO);
            log.info("[ImageCaptionServiceImpl] 插入新的ImageCaptionVO, id:{}, imageId:{}", targetImageCaptionVO.getId(), imageVO.getId());
        } else {
            // 已存在记录，执行更新
            this.updateByIdSelective(targetImageCaptionVO);
            log.info("[ImageCaptionServiceImpl] 更新已存在的ImageCaptionVO, id:{}, imageId:{}", targetImageCaptionVO.getId(), imageVO.getId());
        }

        if (initEmbeddings) {
            this.initEmbeddingsByCaption(targetImageCaptionVO, ClothTypeEnum.TwoPiece, false);
        }

        return targetImageCaptionVO;
    }

    @Nullable
    private static ImageAnalysisCaption getImageAnalysisCaption(List<ImageCaptionUserVO> imageCaptionUserVOS) {
        // 使用MapUtil.nest方法将扁平的analysis key转换为嵌套的标准JSON结构
        Map<String, Object> nestedCaptionMap = MapUtil.rebuildNestedJson(imageCaptionUserVOS.getFirst().getCaption().toJSONString());

        // 提取analysis对象并转换为ImageAnalysisCaption
        Object analysisObj = nestedCaptionMap.get("analysis");
        log.info("[ImageCaptionServiceImpl] gemini-flash已打标, nestedCaptionMap:{}, analysisObj:{}", JSONObject.toJSONString(nestedCaptionMap), analysisObj);

        ImageAnalysisCaption analysisCaption;
        if (analysisObj instanceof Map) {
            // 将Map转换为ImageAnalysisCaption对象
            String analysisJson = JSON.toJSONString(analysisObj);
            analysisCaption = JSON.parseObject(analysisJson, ImageAnalysisCaption.class);
        } else {
            // 如果已经是ImageAnalysisCaption对象，直接使用
            analysisCaption = (ImageAnalysisCaption) analysisObj;
        }
        return analysisCaption;
    }

    private List<ImageCaptionVO> getExistingCaptions(ImageVO imageVO) {
        ImageCaptionQuery query = new ImageCaptionQuery();
        query.setImageId(imageVO.getId());
        query.setPageNum(1);
        query.setPageSize(1);
        query.setOrderBy("id desc");
        List<ImageCaptionVO> existingCaptions = this.queryImageCaptionList(query);
        return existingCaptions;
    }

    private List<ImageCaptionUserVO> getGeminiFlashImageCaptionUsers(ImageVO imageVO, CaptionUserVO geminiFlash) {
        ImageCaptionUserQuery imageCaptionUserQuery = new ImageCaptionUserQuery();
        imageCaptionUserQuery.setImageId(imageVO.getId());
        imageCaptionUserQuery.setUserId(geminiFlash.getId());
        imageCaptionUserQuery.setPageNum(1);
        imageCaptionUserQuery.setPageSize(1);
        imageCaptionUserQuery.setOrderBy("id desc");

        return imageCaptionUserService.queryImageCaptionUserList(imageCaptionUserQuery);
    }

    /**
     * 根据结构化描述构造服装文本，这个文本结构要和阿九那边一致
     *
     * @param clothAnalysis 服装分析数据
     * @param clothType     服装类型
     * @return 格式化的服装描述文本
     */
    @Override
    public String getClothFeatureTextByAnalysis(ImageAnalysisCaption clothAnalysis, ClothTypeEnum clothType) {
        if (clothAnalysis == null) {
            log.error("服装分析数据为空，无法构造服装文本");
            return "";
        }

        if (clothType == null) {
            log.error("服装类型为空，无法构造服装文本");
            return "";
        }

        ImageAnalysisCaption.Clothing clothing = clothAnalysis.getClothing();
        List<String> descriptionParts = new ArrayList<>();

        // 根据服装类型决定提取哪些信息
        String targetClothingType = mapClothTypeToTarget(clothType);

        // --- 提取上衣信息 ---
        if (("Top".equals(targetClothingType) || "Suit".equals(targetClothingType)) &&
                clothing.getTop() != null && isValidValue(clothing.getTop().getType())) {

            ImageAnalysisCaption.Top top = clothing.getTop();
            List<String> topDescriptionElements = new ArrayList<>();

            if (isValidValue(top.getColor())) {
                topDescriptionElements.add("a " + top.getColor() + "-colored");
            }
            if (isValidValue(top.getLength())) {
                topDescriptionElements.add(top.getLength());
            }
            if (isValidValue(top.getType())) {
                topDescriptionElements.add(top.getType());
            }
            if (isValidValue(top.getFit())) {
                topDescriptionElements.add(", with a " + top.getFit() + " fit");
            }
            if (isValidValue(top.getSleeveLength())) {
                topDescriptionElements.add(", " + top.getSleeveLength());
            }
            if (isValidValue(top.getStyle())) {
                topDescriptionElements.add(", style description: " + top.getStyle());
            }
            if (isValidValue(top.getPatternAndFeature())) {
                topDescriptionElements.add(", features: " + top.getPatternAndFeature());
            }

            if (!topDescriptionElements.isEmpty()) {
                descriptionParts.add("Top: " + String.join("", topDescriptionElements));
            }
        }

        // --- 提取下装信息 ---
        if (("Bottom".equals(targetClothingType) || "Suit".equals(targetClothingType)) &&
                clothing.getBottom() != null && isValidValue(clothing.getBottom().getType())) {

            ImageAnalysisCaption.Bottom bottom = clothing.getBottom();
            List<String> bottomDescriptionElements = new ArrayList<>();

            if (isValidValue(bottom.getColor())) {
                bottomDescriptionElements.add("a " + bottom.getColor() + "-colored");
            }
            if (isValidValue(bottom.getLength())) {
                bottomDescriptionElements.add(bottom.getLength());
            }
            if (isValidValue(bottom.getType())) {
                bottomDescriptionElements.add(bottom.getType());
            }
            if (isValidValue(bottom.getStyle())) {
                bottomDescriptionElements.add(", style description: " + bottom.getStyle());
            }
            if (isValidValue(bottom.getPatternAndFeature())) {
                bottomDescriptionElements.add(", features: " + bottom.getPatternAndFeature());
            }

            if (!bottomDescriptionElements.isEmpty()) {
                descriptionParts.add("Bottom: " + String.join("", bottomDescriptionElements));
            }
        }

        // 返回结果
        if (descriptionParts.isEmpty()) {
            log.error("No information found for the specified clothing type: {}.", targetClothingType);
            return null;
        } else {
            return String.join(" ", descriptionParts);
        }
    }

    /**
     * 根据结构化描述构造服装文本，这个文本结构要和阿九那边一致
     */
    @Override
    public String getClothStyleDescription(ImageAnalysisCaption captionModel) {

        // 从结构化描述中提取款式相关字段
        if (captionModel == null) {
            return "";
        }

        StringBuilder styleDesc = new StringBuilder();
        if (captionModel.getClothing().getTop() != null) {
            ImageAnalysisCaption.Top top = captionModel.getClothing().getTop();
            styleDesc.append("Top: ").append(top.getStyle()).append(" ");
        }

        if (captionModel.getClothing().getBottom() != null) {
            ImageAnalysisCaption.Bottom bottom = captionModel.getClothing().getBottom();
            styleDesc.append("Bottom: ").append(bottom.getStyle()).append(" ");
        }

        return styleDesc.toString().trim();
    }

    /**
     * 将 ClothTypeEnum 映射为目标服装类型字符串
     */
    private String mapClothTypeToTarget(ClothTypeEnum clothType) {
        if (clothType == null) {
            return null;
        }

        switch (clothType) {
            case Tops:
                return "Top";
            case Bottoms:
                return "Bottom";
            case TwoPiece:
            case SwimSuit:
            case OnePiece:
            case SexyLingerie:
                return "Suit";
            default:
                return null;
        }
    }

    /**
     * 检查值是否有效（非空且不为"None"）
     */
    private boolean isValidValue(String value) {
        return value != null && !value.trim().isEmpty() && !"null".equalsIgnoreCase(value.trim())
                && !"None".equalsIgnoreCase(value.trim());
    }

    /**
     * 初始化缓存
     */
    @Override
    public void initCacheOnStartup() {
        // 缓存已经初始化完成，直接返回
        if (cacheInitialized.get()) {
            log.info("ImageCaption缓存已经初始化完成，跳过重复初始化");
            return;
        }

        // 确保只有一个线程能执行初始化
        if (cacheLoading.compareAndSet(false, true)) {
            try {
                // 双重检查，防止在获取锁的过程中其他线程已经完成初始化
                if (cacheInitialized.get()) {
                    log.info("ImageCaption缓存已经初始化完成，跳过重复初始化");
                    return;
                }

                // 记录初始内存使用情况
                Runtime runtime = Runtime.getRuntime();
                long initialUsedMemory = runtime.totalMemory() - runtime.freeMemory();

                log.info("开始异步加载ImageCaption数据到内存缓存");
                long startTime = System.currentTimeMillis();

                // 分批加载数据，避免一次性加载过多数据导致内存溢出
                int batchSize = 2000;
                int offset = 0;
                int totalLoaded = 0;

                while (true) {
                    ImageCaptionQuery query = new ImageCaptionQuery();
                    query.setPageNum((offset / batchSize) + 1);
                    query.setPageSize(batchSize);

                    List<ImageCaptionVO> batch = queryImageCaptionList4Recall(query);
                    if (CollectionUtils.isEmpty(batch)) {
                        break;
                    }

                    // 将数据加载到缓存
                    for (ImageCaptionVO caption : batch) {
                        if (caption.getId() != null) {
                            imageCaptionCache.put(caption.getId(), caption);
                        }
                    }

                    totalLoaded += batch.size();
                    offset += batchSize;

                    // 如果批次数据少于batchSize，说明已经加载完毕
                    if (batch.size() < batchSize) {
                        break;
                    }
                }

                cacheInitialized.set(true);

                // 记录缓存初始化完成后的内存使用情况
                long finalUsedMemory = runtime.totalMemory() - runtime.freeMemory();
                long cacheMemoryUsage = finalUsedMemory - initialUsedMemory;
                long endTime = System.currentTimeMillis();
                log.info("ImageCaption缓存初始化完成，共加载{}条数据，耗时{}ms，缓存占用内存: {}MB，当前总内存使用: {}MB",
                        totalLoaded,
                        endTime - startTime,
                        String.format("%.2f", cacheMemoryUsage / (1024.0 * 1024.0)),
                        String.format("%.2f", finalUsedMemory / (1024.0 * 1024.0)));

            } catch (Exception e) {
                log.error("ImageCaption缓存初始化失败", e);
            } finally {
                cacheLoading.set(false);
            }
        }
    }

    /**
     * 从缓存中查询ImageCaption数据（优化版本的queryByStyleVectorSimilarity）
     * 如果缓存未初始化，则回退到数据库查询
     */
    public List<ImageCaptionVO> queryByStyleVectorSimilarityFromCache(PGvector styleVector, ClothGenderEnum gender,
            double similarityThreshold, int limit, String genre, AgeGroupEnum ageGroup,
            Set<Integer> excludeImageCaptionIds) {
        // 如果缓存未初始化，回退到数据库查询
        if (!cacheInitialized.get()) {
            log.info("queryByStyleVectorSimilarityFromCache缓存未初始化，使用数据库查询");
            return queryByStyleVectorSimilarity(styleVector, gender, similarityThreshold, limit, genre, ageGroup,
                    excludeImageCaptionIds);
        }

        try {
            List<ImageCaptionVO> candidates = new ArrayList<>();

            // 从缓存中筛选符合条件的数据
            for (ImageCaptionVO caption : imageCaptionCache.values()) {
                // 排除指定的imageCaptionId
                if (excludeImageCaptionIds != null && excludeImageCaptionIds.contains(caption.getId())) {
                    continue;
                }

                // 检查基本条件
                if (!StringUtils.equals(gender.getCode(), caption.getClothGenderType())
                        || !StringUtils.equals("scene", caption.getImageType())
                        || !StringUtils.equals(ageGroup.getCode(), caption.getAgeGroup())) {
                    continue;
                }

                // 检查流派条件
                if (genre != null && caption.getCaption() != null) {
                    String captionGenre = null;
                    try {
                        if (caption.getCaption().getShootingTheme() != null) {
                            captionGenre = caption.getCaption().getShootingTheme().getGenre();
                        }
                    } catch (Exception e) {
                        log.debug("解析caption流派信息失败: {}", e.getMessage());
                    }

                    if (!genre.equals(captionGenre)) {
                        continue;
                    }
                }

                // 计算向量相似度
                if (caption.getClothStyleTextEmb() != null) {
                    try {
                        double similarity = VectorUtil.innerProduct(styleVector, caption.getClothStyleTextEmb());
                        if (similarity >= similarityThreshold) {
                            caption.setClothStyleSimilarity(similarity);
                            candidates.add(caption);
                        }
                    } catch (Exception e) {
                        log.debug("计算向量相似度失败: {}", e.getMessage());
                    }
                }
            }

            // 按相似度排序并限制数量
            candidates.sort((a, b) -> Double.compare(
                    b.getClothStyleSimilarity() != null ? b.getClothStyleSimilarity() : 0.0,
                    a.getClothStyleSimilarity() != null ? a.getClothStyleSimilarity() : 0.0));

            if (candidates.size() > limit) {
                candidates = candidates.subList(0, limit);
            }

            log.info("从缓存查询到{}条匹配数据（排除{}个ID），gender:{}, similarity:{}, limit:{}, genre:{}",
                    candidates.size(), excludeImageCaptionIds != null ? excludeImageCaptionIds.size() : 0,
                    gender.getCode(), similarityThreshold, limit, genre);

            return candidates;

        } catch (Exception e) {
            log.error("从缓存查询数据失败，回退到数据库查询", e);
            return queryByStyleVectorSimilarity(styleVector, gender, similarityThreshold, limit, genre, ageGroup,
                    excludeImageCaptionIds);
        }
    }

    /**
     * 等待缓存初始化完成（用于测试）
     * 
     * @param maxWaitSeconds 最大等待时间（秒）
     * @return 是否初始化完成
     */
    public boolean waitForCacheInitialization(int maxWaitSeconds) {
        if (cacheInitialized.get()) {
            return true;
        }

        log.info("线程等待ImageCaption缓存初始化完成，最大等待时间: {} 秒", maxWaitSeconds);

        int waitedSeconds = 0;
        while (!cacheInitialized.get() && waitedSeconds < maxWaitSeconds) {
            try {
                Thread.sleep(1000); // 每秒检查一次
                waitedSeconds++;

                if (waitedSeconds % 10 == 0) {
                    log.info("线程已等待缓存初始化 {} 秒", waitedSeconds);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("线程在等待缓存初始化时被中断");
                return false;
            }
        }

        boolean initialized = cacheInitialized.get();
        if (initialized) {
            log.info("线程等待缓存初始化完成，总等待时间: {} 秒", waitedSeconds);
        } else {
            log.warn("线程等待缓存初始化超时，等待时间: {} 秒", waitedSeconds);
        }

        return initialized;
    }
}