package ai.conrain.aigc.platform.service.model.vo;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * SearchTaskVO
 *
 * @version SearchTaskService.java
 */
@Data
public class SearchTaskVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 主键 */
	private Integer id;

	/** MuseGate用户id */
	private Integer userId;

	/** userSessionId */
	private Integer userSessionId;

	/** userSessionTaskId */
	private Integer userSessionTaskId;

	/** 用户上传的衣服图片信息,json格式,url/clothGender/clothType等 */
	private String clothImgDetail;

	/** 衣服图片分析结果,json格式,打标结果 */
	private String clothAnalysis;

	/** 用户上传的参考图片信息,json array格式,url/refScopes等 */
	private String refImgDetail;

	/** 参考图片分析结果,json array格式,打标结果 */
	private String refImgAnalysis;

	/** 搜索参数,json格式 */
	private String searchOptions;

	/** 搜索状态 */
	private String status;

	/** 搜索结果批次摘要 */
	private String retSummary;

	/** 扩展字段 */
	private String extInfo;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

}