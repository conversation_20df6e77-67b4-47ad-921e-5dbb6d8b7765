/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 年龄范围枚举
 *
 * <AUTHOR>
 * @version : AgeRangeEnum.java, v 0.1 2025/4/11 23:02 renxiao.wu Exp $
 */
@Getter
public enum AgeRangeEnum {
    BIG_CHILD("big-child", "大童", "eleven-year-old"),
    MEDIUM_CHILD("medium-child", "中童", "eight-year-old"),
    SMALL_CHILD("small-child", "小童", "four-year-old"),
    INFANT_CHILD("infant-child", "幼童", "two-year-old"),
    TEENAGER("teenager", "青少年", "fourteen-year-old"),
    ADULT("adult", "成人", ""),
    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    /** 年龄描述 */
    private final String ageDesc;

    private AgeRangeEnum(String code, String desc, String ageDesc) {
        this.code = code;
        this.desc = desc;
        this.ageDesc = ageDesc;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static AgeRangeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (AgeRangeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static AgeRangeEnum getByCodeWithDefault(String code, AgeRangeEnum defaultEnum) {
        AgeRangeEnum result = getByCode(code);
        return result == null ? defaultEnum : result;
    }
}
