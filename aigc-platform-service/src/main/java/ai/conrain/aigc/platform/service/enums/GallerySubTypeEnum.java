package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

@Getter
public enum GallerySubTypeEnum {

    BAG("BAG", "包包"),

    WATCH("WATCH", "手表"),

    NECKLACE("NECKLACE", "项链"),

    BRACELET("BR<PERSON><PERSON>ET", "手链/手镯"),

    EARRING("EARRING","耳饰"),
    ;

    private String code;

    private String name;

    GallerySubTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static GallerySubTypeEnum getByCode(String code) {
        for (GallerySubTypeEnum type : GallerySubTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
