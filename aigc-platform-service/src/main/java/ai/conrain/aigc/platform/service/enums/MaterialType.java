package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum MaterialType {
    cloth("cloth", "衣服素材", "CLOTH_LABEL"),
    face("face", "模特素材", "MODEL_LABEL"),
    scene("scene", "场景素材", "SCENE_LABEL");;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    /** 打标任务类型 */
    private final String labelTaskType;

    private MaterialType(String code, String desc,String labelTaskType) {
        this.code = code;
        this.desc = desc;
        this.labelTaskType = labelTaskType;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static MaterialType getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (MaterialType item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}
