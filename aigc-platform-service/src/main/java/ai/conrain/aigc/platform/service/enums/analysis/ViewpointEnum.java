/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums.analysis;

import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 拍摄方位枚举
 *
 * <AUTHOR>
 * @version : ViewpointEnum.java, v 0.1 2025/8/24 23:58 renxiao.wu Exp $
 */
@Getter
public enum ViewpointEnum {
    FRONTAL_VIEW("Frontal_view", "正面", CameraAngleEnum.FRONT_VIEW),
    PROFILE_VIEW("Profile_view", "侧面", CameraAngleEnum.FRONT_VIEW),
    BACK_VIEW("Back_view", "背面", CameraAngleEnum.BACK_VIEW),
    NONE("None", "其他", CameraAngleEnum.FRONT_VIEW),
    SIDE_90_VIEW("Side_90_view", "90度侧", CameraAngleEnum.FRONT_VIEW),
    BACK_PROFILE_VIEW("Back_profile_view", "背侧", CameraAngleEnum.BACK_VIEW),
    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    private final CameraAngleEnum cameraAngle;

    private ViewpointEnum(String code, String desc, CameraAngleEnum cameraAngle) {
        this.code = code;
        this.desc = desc;
        this.cameraAngle = cameraAngle;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static ViewpointEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (ViewpointEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code         枚举码
     * @param defaultValue 默认枚举
     * @return 对应枚举
     */
    public static ViewpointEnum getByCode(String code, ViewpointEnum defaultValue) {
        if (StringUtils.isBlank(code)) {
            return defaultValue;
        }

        for (ViewpointEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return defaultValue;
    }

}
