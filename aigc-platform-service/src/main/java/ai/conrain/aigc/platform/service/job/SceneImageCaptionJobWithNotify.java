package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class SceneImageCaptionJobWithNotify extends JavaProcessor {

    @Autowired
    private ImageService imageService;

    @Autowired
    private ImageCaptionService imageCaptionService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        // 任务参数
        String instanceParameters = context.getInstanceParameters();

        log.info("[SceneImageCaptionJobWithNotify] start, instanceParameters: {}", instanceParameters);

        int count = 15;
        if (CommonUtil.isValidJson(instanceParameters)) {
            count = JSONObject.parseObject(instanceParameters).getInteger("count");
        }

        try {
            List<ImageVO> imageVOS = imageService.selectUncaptionedSceneImages4Embedding(count);
            if (CollectionUtils.isEmpty(imageVOS)) {
                log.info("[SceneImageCaptionJobWithNotify] no uncaptioned scene images");
                return new ProcessResult(true);
            }

            for (ImageVO imageVO : imageVOS) {
                MDC.put("traceId", uuid + "_" + imageVO.getId());
                try {
                    imageCaptionService.createImageCaptionTaskWithNotify(imageVO);
                } catch (Exception e){
                    log.error("[SceneImageCaptionJobWithNotify] error", e);
                }
            }

        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }
}
