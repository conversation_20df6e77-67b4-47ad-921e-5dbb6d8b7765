package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.component.annotation.Roles;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchElementsQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeElementQuery;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.UserFavorQuery;
import ai.conrain.aigc.platform.service.model.request.ElementConfigRequest;
import ai.conrain.aigc.platform.service.model.request.IntelligentRecommendationRequest;
import ai.conrain.aigc.platform.service.model.request.QueryAutoGenElementRecommendRequest;
import ai.conrain.aigc.platform.service.model.request.ResetOrderRequest;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.SecurityUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.*;

/**
 * Creative元素控制器
 *
 * <AUTHOR>
 * @version CreativeElementController.java v 0.1 2024-05-08 03:35:56
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/element")
public class CreativeElementController {
    /** 允许输出的扩展字段白名单 */
    private static final List<String> outputKeyWhitelist = Arrays.asList(clothStyleImgs, faces, clothStyles,
        logoLocation, clothStyleType, KEY_CLOTH_COLOR, KEY_MASK_IMAGE, KEY_OPEN_SCOPE, SHOW_IMGS, KEY_EXPERIMENTAL,
        KEY_DELIVERY_TIME, IS_SET_POSTURE);

    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private CreativeBatchElementsService creativeBatchElementsService;
    @Autowired
    private CreativeTaskService creativeTaskService;
    @Autowired
    private DistributorCustomerService distributorCustomerService;
    @Autowired
    private UserService userService;
    @Lazy
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private UserFavorService userFavorService;

    private final Cache<Integer, List<DistributorCustomerVO>> distributorCustomerCache = CacheBuilder.newBuilder()
        .expireAfterWrite(10, TimeUnit.SECONDS).build();

    @GetMapping("/getById/{id}")
    public Result<CreativeElementVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(creativeElementService.selectByIdWithChildren(id));
    }

    @PostMapping("/batchQueryById")
    public Result<Map<Integer, CreativeElementVO>> batchQueryById(@RequestBody CreativeElementQuery query) {
        Map<Integer, CreativeElementVO> result = new HashMap<>();

        // 批量查询
        List<Integer> idList = query.getIdList();
        if (!CollectionUtils.isEmpty(idList)) {
            idList.forEach(id -> {
                CreativeElementVO elementVO = creativeElementService.selectPrimaryInfoByIdWithChildren(id);
                result.put(id, elementVO);
            });
        }

        return Result.success(result);
    }

    @PostMapping("/queryHasShowImageChildren")
    public Result<Map<Integer, CreativeElementVO>> queryHasShowImageChildrenIds(
        @RequestBody CreativeElementQuery query) {
        // 最大数量
        int MAX_COUNT = 20;

        Map<Integer, CreativeElementVO> result = new HashMap<>();

        // 查询当前用户所有专属场景
        List<CreativeElementVO> exclusiveElementList = creativeElementService.queryUserExclusiveElement(query,
            MAX_COUNT);
        // 提取 id
        List<Integer> exclusiveIds = exclusiveElementList.stream().map(CreativeElementVO::getId).collect(
            Collectors.toList());
        // 查询专属场景的数据
        if (CollectionUtils.isNotEmpty(exclusiveIds)) {
            exclusiveIds.forEach(id -> {
                CreativeElementVO elementVO = creativeElementService.selectPrimaryInfoByIdWithChildren(id);
                // 专属场景中 Child 不为空时
                if (!elementVO.getChildren().isEmpty()) {
                    result.put(id, elementVO);
                }
            });
        }

        // 若目前的场景数量不足最大值，则继续查询普通场景
        if (result.size() < MAX_COUNT) {
            // 查询含有参考图的场景
            List<Integer> hasChildrenIds = creativeBatchElementsService.queryHasShowImageChildrenIds(query,
                MAX_COUNT - result.size());
            hasChildrenIds.forEach(id -> {
                CreativeElementVO elementVO = creativeElementService.selectPrimaryInfoByIdWithChildren(id);
                result.put(id, elementVO);
            });
        }

        return Result.success(result);
    }

    @PostMapping("/getConfig")
    public Result<List<CreativeElementVO>> getConfig(@NotBlank @JsonArg String type, @JsonArg String bizType,
                                                     @JsonArg Boolean onlyExclusive, @JsonArg Boolean needAll) {
        CreativeTypeEnum creativeType = CreativeTypeEnum.getByCode(type);
        AssertUtil.assertNotNull(creativeType, ResultCode.PARAM_INVALID, "创作类型错误");
        // 获取bizType, 默认是 ALL
        CreativeBizTypeEnum bizTypeEnum = ObjectUtils.defaultIfNull(CreativeBizTypeEnum.getByCode(bizType),
            CreativeBizTypeEnum.ALL);
        long startTime = System.currentTimeMillis();

        List<CreativeElementVO> data = null;
        if (onlyExclusive != null && onlyExclusive) {
            data = creativeElementService.queryAllByLevelOnlyExclusive(creativeType, bizTypeEnum,
                needAll != null ? needAll : false);
        } else {
            data = creativeElementService.queryAllByLevel(creativeType, bizTypeEnum, needAll != null ? needAll : false);
        }

        // 换头换背景任务只需要 faceLora 的人脸
        if (creativeType == CreativeTypeEnum.FACE_SCENE_SWITCH) {
            data.forEach(e -> {
                if (e.getConfigKey().equals(ElementConfigKeyEnum.FACE.name())) {
                    e.getChildren().removeIf(c -> StringUtils.isBlank(c.getExtInfo(KEY_FACE_LORA, String.class)));
                }
            });
        }

        log.info("获取配置信息，加载所有元素,type={},rt={}ms", type, System.currentTimeMillis() - startTime);

        return Result.success(pruneForFront(data));
    }

    @PostMapping("/getBasicChangeConfig")
    public Result<List<CreativeElementVO>> getBasicChangeConfig(@RequestBody @Valid ElementConfigRequest request) {
        // 参数提取
        String bizType = request.getBizType();
        Boolean onlyExclusive = request.getOnlyExclusive();
        List<String> configKeys = request.getConfigKeys();

        // 获取bizType, 默认是 ALL
        CreativeBizTypeEnum bizTypeEnum = ObjectUtils.defaultIfNull(CreativeBizTypeEnum.getByCode(bizType),
            CreativeBizTypeEnum.ALL);
        long startTime = System.currentTimeMillis();

        List<CreativeElementVO> data = null;
        if (onlyExclusive != null && onlyExclusive) {
            data = creativeElementService.queryAllByLevelOnlyExclusive(configKeys, bizTypeEnum);
        } else {
            data = creativeElementService.queryAllByLevel(configKeys, bizTypeEnum);
        }

        log.info("[基础款换衣]获取配置信息，加载所有元素,rt={}ms", System.currentTimeMillis() - startTime);

        return Result.success(pruneForFront(data));
    }

    @GetMapping("/getDefaultTestCreationConfig")
    public Result<DefaultTestCreationConfig> getDefaultTestCreationConfig() {

        // 女模默认：sara，男模默认：lester。场景默认：北美街拍
        List<CreativeElementVO> faces = creativeElementService.queryRootKey(ElementConfigKeyEnum.FACE.name())
            .getChildren();
        List<CreativeElementVO> scenes = creativeElementService.queryRootKey(ElementConfigKeyEnum.SCENE.name())
            .getChildren();

        CreativeElementVO saraFace = faces.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getName(), "sara"))
            .findFirst().orElse(null);
        CreativeElementVO lesterFace = faces.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getName(), "lester"))
            .findFirst().orElse(null);

        CreativeElementVO northAmericaScene = scenes.stream().filter(
            e -> StringUtils.equalsIgnoreCase(e.getName(), "北美街拍")).findFirst().orElse(null);

        DefaultTestCreationConfig ret = new DefaultTestCreationConfig();
        if (lesterFace != null) {
            ret.setMaleFaces(Collections.singletonList(lesterFace));
        }
        if (saraFace != null) {
            ret.setFemaleFaces(Collections.singletonList(saraFace));
        }
        if (northAmericaScene != null) {
            ret.setSceneList(Collections.singletonList(northAmericaScene));
        }

        return Result.success(ret);
    }

    @GetMapping("/getClothColorCfg")
    public Result<JSONArray> getClothColorCfg() {
        return Result.success(
            JSONArray.parseArray(systemConfigService.queryValueByKey(SystemConstants.CLOTH_COLOR_TYPE_CFG)));
    }

    @GetMapping("/getTypes/{key}")
    public Result<JSONArray> getTypesByKey(@NotNull @PathVariable("key") String key) {
        String systemKey = ElementConfigKeyEnum.getSystemConfigKey(key);
        return getConfigJsonArray(systemKey);
    }

    @GetMapping("/getClothCategoryCfg")
    public Result<JSONArray> getClothCategoryCfg() {
        return Result.success(
            JSONArray.parseArray(systemConfigService.queryValueByKey(SystemConstants.CLOTH_CATEGORY_CFG)));
    }

    /**
     * 获取后台的场景适合的服装类型配置
     * <p>
     * 袖子长短：无袖、短袖、中袖、7分袖、长袖
     * 衣服长短：短款（肚脐眼）、及腰款（屁股上沿）、长款（屁股下沿）
     * 衣服形状：修身、正常、廓形
     * 裤子长短：短裤、膝盖上、膝盖下、7分裤、9分裤、正常（及鞋）、拖地
     * 裤子形状：legging，正常(直筒)，阔腿，喇叭，镰刀，裙裤
     */
    @GetMapping("/getClothTypeScopeCfg4Scene")
    public Result<JSONArray> getClothTypeScopeCfg4Scene() {
        return getConfigJsonArray(SystemConstants.SCENE_CLOTH_SCOPE_CFG);
    }

    @PostMapping("/editElementType")
    public Result<?> editElementType(@JsonArg @NotNull JSONArray data, @JsonArg @NotBlank String key) {
        String systemKey = ElementConfigKeyEnum.getSystemConfigKey(key);

        SystemConfigVO config = systemConfigService.queryByKey(systemKey);
        config.setConfValue(data.toString());
        systemConfigService.updateById(config);
        return Result.success();
    }

    @PostMapping("/getMerchantRecentSceneElements")
    public Result<List<CreativeElementVO>> getMerchantRecentSceneElements(@RequestBody CreativeElementQuery query) {
        CreativeTypeEnum creativeType = CreativeTypeEnum.getByCode(query.getType());
        AssertUtil.assertNotNull(creativeType, ResultCode.PARAM_INVALID, "创作类型错误");
        CreativeBizTypeEnum bizTypeEnum = ObjectUtils.defaultIfNull(CreativeBizTypeEnum.getByCode(query.getBizType()),
            CreativeBizTypeEnum.ALL);
        List<CreativeElementVO> list;

        Integer userId = query.getUserId() != null ? query.getUserId() : OperationContextHolder.getOperatorUserId();
        query.setUserId(userId);

        if (creativeType == CreativeTypeEnum.FACE_SCENE_SWITCH) {
            list = creativeBatchElementsService.queryRecentFaceLora(userId);
        } else {
            list = queryRecentElements(query, 10, bizTypeEnum);
        }

        return Result.success(pruneForFront(list));
    }

    @PostMapping("/queryFavorElements")
    public Result<?> queryFavorElements(@RequestBody CreativeElementQuery query) {
        UserFavorQuery favorQuery = new UserFavorQuery();
        favorQuery.setType(FavorTypeEnum.ELEMENT.getCode());
        favorQuery.setOperatorId(OperationContextHolder.getOperatorUserId());
        favorQuery.setPageNum(1);
        favorQuery.setPageSize(100);
        PageInfo<UserFavorVO> userFavors = userFavorService.queryUserFavorByPage(favorQuery);

        if (CollectionUtils.isEmpty(userFavors.getList())) {
            return Result.success();
        }

        List<Integer> ids = userFavors.getList().stream().map(UserFavorVO::getItemId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return Result.success();
        }

        if (CollectionUtils.isNotEmpty(query.getIds())) {
            ids.removeIf(e -> !query.getIds().contains(e));
        }
        query.setIds(ids);

        // TODO 这里可以先通过userFavors过滤一波，待优化
        PageInfo<CreativeElementVO> pageInfo = creativeElementService.queryByPage(query);

        List<CreativeElementVO> data = pruneForFront(pageInfo.getList());
        pageInfo.setList(data);

        return Result.success(pageInfo);
    }

    @GetMapping("/getByKey/{key}")
    public Result<List<CreativeElementVO>> getConfigByType(@NotNull @PathVariable("key") String key) {

        boolean needChildren = !(StringUtils.equals(key, ElementConfigKeyEnum.FACE.name()) || StringUtils.equals(key,
            ElementConfigKeyEnum.SCENE.name()));

        List<CreativeElementVO> data = creativeElementService.queryByLevelAndKey(key, needChildren);

        if (CollectionUtils.isEmpty(data)) {
            return Result.success(data);
        }

        // 补充修改人、操作人昵称
        List<UserVO> ops = userService.batchQueryById(
            data.stream().map(CreativeElementVO::getOperatorId).collect(Collectors.toList()));

        data.forEach(item -> item.setOperatorNick(ops.stream().filter(user -> user.getId().equals(item.getOperatorId()))
            .findFirst().map(UserVO::getNickName).orElse(null)));

        // 补充客户昵称
        List<UserVO> users = userService.batchQueryById(
            data.stream().map(CreativeElementVO::getUserId).collect(Collectors.toList()));

        data.forEach(item -> {
            item.setUserNick(users.stream().filter(user -> user.getId().equals(item.getUserId())).findFirst()
                .map(UserVO::getNickName).orElse(null));
            item.setUserRoleType(users.stream().filter(user -> user.getId().equals(item.getUserId())).findFirst()
                .map(UserVO::getRoleType).orElse(null));
        });

        // 首先根据 isNew 是否为 true 进行排序，若都为 true 则根据 id 降序排序
        data.sort(Comparator.comparing(CreativeElementVO::getIsNew, Comparator.naturalOrder())
            .thenComparingInt(CreativeElementVO::getId).reversed());

        if (StringUtils.equals(key, ElementConfigKeyEnum.FACE.name()) || StringUtils.equals(key,
            ElementConfigKeyEnum.SCENE.name())) {

            Map<Integer, String> userNickMap = userService.batchQueryById(data.stream().map(
                    e -> ObjectUtils.defaultIfNull(e.getRelatedOperatorUserId(), e.getUserId())).distinct()
                .collect(Collectors.toList())).stream().collect(Collectors.toMap(UserVO::getId, UserVO::getNickName));

            data.forEach(e -> {
                if (e.getRelatedOperatorUserId() == null) {
                    e.setRelatedOperatorUserId(e.getUserId());
                }
                e.setRelatedOperatorUserNick(userNickMap.get(e.getRelatedOperatorUserId()));
            });
        }

        if (StringUtils.equals(key, ElementConfigKeyEnum.SCENE.name())) {
            MaterialModelQuery query = new MaterialModelQuery();
            query.setIds(data.stream().map(CreativeElementVO::getLoraModelId).collect(Collectors.toList()));
            Map<Integer, MaterialModelVO> modelMap = materialModelService.queryListWithBlogs(query).stream().collect(
                Collectors.toMap(MaterialModelVO::getId, item -> item));

            data.forEach(e -> {

                MaterialModelVO model = modelMap.get(e.getLoraModelId());
                String contentOrStyle = model != null && model.getClothLoraTrainDetail() != null
                    ? model.getClothLoraTrainDetail().getContentOrStyle() : null;
                e.setContentOrStyle(contentOrStyle);
            });
        }

        return Result.success(data);
    }

    @PostMapping("/create")
    public Result<?> create(@Valid @RequestBody CreativeElementVO request) {
        request.setOperatorId(OperationContextHolder.getOperatorUserId());
        request.setBelong(ModelTypeEnum.SYSTEM);

        return Result.success(creativeElementService.insert(request));
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@JsonArg @NotNull Integer id) {
        creativeElementService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody CreativeElementVO request) {
        CreativeElementVO origin = creativeElementService.selectById(request.getId());
        AssertUtil.assertNotNull(origin, ResultCode.PARAM_INVALID, "未找到元素");

        JSONObject temp = request.getExtInfo();
        request.setExtInfo(origin.getExtInfo());

        if (temp != null) {
            request.getExtInfo().putAll(temp);
        }
        request.setOperatorId(OperationContextHolder.getOperatorUserId());
        creativeElementService.updateById(request);
        return Result.success();
    }

    @PostMapping("/order/reset")
    public Result<?> resetOrder(@Valid @RequestBody ResetOrderRequest request) {
        creativeElementService.resetOrder(request);
        return Result.success();
    }

    @PostMapping("/queryPubViewByPage")
    public Result<?> queryPubViewByPage(@RequestBody CreativeElementQuery query) {
        query.setStatus(ElementStatusEnum.PROD.name());
        return this.queryByPage(query);
    }

    @PostMapping("/queryByPage")
    public Result<?> queryByPage(@RequestBody CreativeElementQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        String key = query.getConfigKey();
        AssertUtil.assertTrue(StringUtils.isNotBlank(key), "configKey不能为空");

        if (!OperationContextHolder.isBackRole()) {
            query.setStatus(ElementStatusEnum.PROD.getCode());

            if (!OperationContextHolder.isDistributorRole() && !OperationContextHolder.isVirtualMerchant()) {
                query.setScopeUserId(OperationContextHolder.getMasterUserId());
            }
        }

        query.setLevel(2);
        PageInfo<CreativeElementVO> result = creativeElementService.queryByPage(query);
        List<CreativeElementVO> data = result.getList();

        if (CollectionUtils.isEmpty(data) || OperationContextHolder.getRoleType() != ADMIN) {
            result.setList(pruneForFront(result.getList()));
            return Result.success(result);
        }

        // 填充用户信息
        if (StringUtils.equals(key, ElementConfigKeyEnum.FACE.name()) || StringUtils.equals(key,
            ElementConfigKeyEnum.SCENE.name())) {

            Map<Integer, String> userNickMap = userService.batchQueryById(data.stream().map(
                    e -> ObjectUtils.defaultIfNull(e.getRelatedOperatorUserId(), e.getUserId())).distinct()
                .collect(Collectors.toList())).stream().collect(Collectors.toMap(UserVO::getId, UserVO::getNickName));

            data.forEach(e -> {
                if (CollectionUtils.isNotEmpty(e.getChildren())) {
                    // 清空level=3的所有子元素
                    e.setChildren(null);
                }

                if (e.getRelatedOperatorUserId() == null) {
                    e.setRelatedOperatorUserId(e.getUserId());
                }
                e.setRelatedOperatorUserNick(userNickMap.get(e.getRelatedOperatorUserId()));
            });
        }

        result.setList(extInfoPruneForFront(result.getList()));

        return Result.success(result);
    }

    @PostMapping("/queryCustomByPage")
    public Result<PageInfo<CreativeElementVO>> queryCustomByPage(@RequestBody CreativeElementQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }

        query.setUserId(OperationContextHolder.getMasterUserId());

        PageInfo<CreativeElementVO> result = creativeElementService.queryCustomByPage(query);
        result.setList(pruneForFront(result.getList()));
        return Result.success(result);
    }

    @PostMapping("/addCustom")
    public Result<?> addCustom(@NotBlank @JsonArg String showImage, @NotBlank @JsonArg String configKey,
                               @JsonArg String tag, @NotBlank @JsonArg String name) {
        CreativeElementVO data = new CreativeElementVO();
        data.setShowImage(showImage);
        data.setConfigKey(configKey);
        data.setStatus(ElementStatusEnum.PROD);
        if (StringUtils.isNotEmpty(tag)) {
            data.setType(Collections.singletonList(tag));
        }
        data.setBelong(ModelTypeEnum.CUSTOM);
        data.setLevel(2);

        // 特殊处理
        if (ElementConfigKeyEnum.REFER == ElementConfigKeyEnum.valueOf(configKey)) {
            data.addExtInfo(KEY_REFER_POSE, CommonUtil.getFileNameFromUrl(showImage));
        }

        CreativeElementVO root = creativeElementService.queryRootKey(configKey);
        AssertUtil.assertNotNull(root, "配置key不存在");
        data.setParentId(root.getId());

        CreativeElementQuery query = new CreativeElementQuery();
        query.setUserId(OperationContextHolder.getMasterUserId());
        query.setConfigKey(configKey);
        long count = creativeElementService.countCustom(query);
        data.setName(name + String.format("%04d", count + 1));

        data.setUserId(OperationContextHolder.getMasterUserId());
        data.setOperatorId(OperationContextHolder.getOperatorUserId());

        return Result.success(creativeElementService.insert(data));
    }

    @PostMapping("/fetchTaskFace")
    public Result<CreativeElementVO> fetchFaceConfig(@NotNull @JsonArg Integer taskId) {
        CreativeTaskVO task = creativeTaskService.selectById(taskId);
        if (task == null) {
            return Result.success();
        }
        AssertUtil.assertNotNull(task, ResultCode.PARAM_INVALID, "创作任务不存在");

        List<CreativeElementVO> list = creativeBatchElementsService.queryBatchElements(task.getBatchId(), false);
        if (CollectionUtils.isEmpty(list)) {
            return Result.success();
        }

        List<CreativeElementVO> faceList = list.stream().filter(
            e -> ElementConfigKeyEnum.FACE.equals(ElementConfigKeyEnum.valueOf(e.getConfigKey()))).collect(
            Collectors.toList());

        if (CollectionUtils.isEmpty(faceList)) {
            return Result.success();
        }

        return Result.success(pruneForFront(faceList).get(0));
    }

    @PostMapping("/clone")
    public Result<CreativeElementVO> clone(@NotNull @JsonArg Integer id, @JsonArg Boolean fullCopy) {
        creativeElementService.clone(id, fullCopy != null ? fullCopy : false);
        return Result.success();
    }

    @PostMapping("/batchCorrectType")
    public Result<?> batchCorrectType(@NotNull @JsonArg String ids) {
        creativeElementService.batchCorrectType(Arrays.stream(ids.split(",")).map(Integer::parseInt)
            .collect(Collectors.toList()));
        return Result.success();
    }

    @PostMapping("/batchInitTrainType")
    public Result<?> batchInitTrainType(@NotNull @JsonArg String ids) {
        creativeElementService.batchInitTrainType(Arrays.stream(ids.split(",")).map(Integer::parseInt)
            .collect(Collectors.toList()));
        return Result.success();
    }

    @PostMapping("/batchCorrectStyle")
    public Result<?> batchCorrectStyle(@NotNull @JsonArg String ids) {
        creativeElementService.batchCorrectLora(Arrays.stream(ids.split(",")).map(Integer::parseInt)
            .collect(Collectors.toList()));
        return Result.success();
    }

    // 上传服装的页面，获取服装推荐的出图场景列表（最多5个）

    @PostMapping("/queryRecommendAutoGenElementsByCloth")
    public Result<List<CreativeElementVO>> queryRecommendAutoGenElements(
        @RequestBody @Valid QueryAutoGenElementRecommendRequest request) {
        List<CreativeElementVO> scenes = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(request.getClothImgUrls())) {
            List<CreativeElementVO> recommendScenes = creativeElementService.queryRecommendAutoGenElementsByCloth(
                request);
            if (recommendScenes != null) {
                scenes.addAll(recommendScenes);
            }
        }

        if (CollectionUtils.size(scenes) < 10) {
            CreativeElementQuery query = new CreativeElementQuery();
            query.setKey(ElementConfigKeyEnum.SCENE.name());
            query.setUserId(OperationContextHolder.getMasterUserId());
            List<CreativeElementVO> recentElements = this.queryRecentElements(query, 10, null);
            if (recentElements != null) {
                scenes.addAll(recentElements);
            }
        }

        return Result.success(pruneForFront(filterByClothStyleType(scenes, request.getClothStyleType())));
    }

    @PostMapping("/updateExperimental")
    public Result<?> updateExperimental(@JsonArg Integer id, @JsonArg boolean experimental) {
        creativeElementService.updateExperimental(id, experimental);
        return Result.success();
    }

    private List<CreativeElementVO> filterByClothStyleType(List<CreativeElementVO> list, String clothStyleType) {
        if (CollectionUtils.isEmpty(list)) {
            return list; // 如果列表为空，直接返回
        }

        List<CreativeElementVO> filteredList = new ArrayList<>();
        for (CreativeElementVO item : list) {
            if (StringUtils.equalsIgnoreCase(clothStyleType, CommonConstants.unisex)) {
                // 如果是 Unisex，检查是否包含 Female 或 Male
                if (item.getType().stream().anyMatch(
                    type -> CommonConstants.female.equalsIgnoreCase(type) || CommonConstants.male.equalsIgnoreCase(
                        type))) {
                    filteredList.add(item);
                }
            } else {
                // 否则，检查是否包含 clothStyleType 或 Common，备注：Common是最早包含童模在内的时候用的，实际场景里很少
                if (item.getType().contains(clothStyleType) || item.getType().contains("Common")) {
                    filteredList.add(item);
                }
            }
        }
        return filteredList;
    }

    /**
     * 获取服装图片服装类型
     *
     * @param request 请求入参
     * @return Result<?>
     */
    @Roles({ADMIN, OPERATOR, DISTRIBUTOR, MERCHANT})
    @PostMapping("/getClothesType")
    public Result<List<String>> getClothesType(@RequestBody @Valid IntelligentRecommendationRequest request) {
        return Result.success(creativeElementService.getClothesType(request));
    }

    /**
     * 智能推荐
     *
     * @param request 请求入参
     * @return Result<?>
     */
    @PostMapping("/intelligentRecommendation")
    public Result<Map<Integer, CreativeElementVO>> intelligentRecommendation(
        @RequestBody @Valid IntelligentRecommendationRequest request) {
        // 查询最大数量，目前固定为20条
        int MAX_COUNT = 20;
        Map<Integer, CreativeElementVO> resultList = creativeElementService.intelligentRecommendation(request,
            MAX_COUNT);
        return Result.success(resultList);
    }

    @GetMapping("/queryNeedProcessCnt")
    public Result<?> queryNeedProcessCnt() {
        Map<String, Integer> map = creativeElementService.countNeedProcessByKeys(
            Arrays.asList(ElementConfigKeyEnum.FACE.name(), ElementConfigKeyEnum.SCENE.name()));
        return Result.success(map);
    }

    /**
     * 批量设置场景动作
     *
     * @param sceneIds 场景 id 列表
     * @return 批量设置结果
     */
    @PostMapping("/batchSetScenePose")
    public Result<?> batchSetScenePose(@NotNull @JsonArg String sceneIds) {
        List<Integer> sceneIdList = Arrays.stream(sceneIds.split(",")).map(Integer::parseInt).collect(
            Collectors.toList());
        creativeElementService.batchSetScenePose(sceneIdList);
        return Result.success();
    }

    /**
     * 查询姿势对应的姿势展示图
     *
     * @param sceneId 场景 id 列表
     * @return 查询结果
     */
    @PostMapping("/selectShowImageByScene")
    public Result<Map<Integer, ShowImageVO>> selectShowImageByScene(@NotNull @JsonArg Integer sceneId) {
        return Result.success(creativeElementService.selectShowImageByScene(sceneId));
    }

    @PostMapping("/assignToUser")
    public Result<?> assignElementToUser(@JsonArg @NotNull Integer elementId, @JsonArg @NotNull Integer userId,
                                         @NotNull @JsonArg Boolean exclusive) {
        creativeElementService.assignToUser(elementId, userId, exclusive != null ? exclusive : false, true);
        return Result.success();
    }

    /**
     * 提取请求参数并构建查询请求
     *
     * @param request 原始请求
     * @return 查询请求对象
     */
    private QueryAutoGenElementRecommendRequest extractAndBuildQueryRequest(IntelligentRecommendationRequest request) {
        // 参数提取
        String clothType = request.getClothType();
        List<String> clothImgUrls = request.getClothImgUrls();

        // 参数封装
        QueryAutoGenElementRecommendRequest queryRequest = new QueryAutoGenElementRecommendRequest();
        queryRequest.setClothType(clothType);
        queryRequest.setClothStyleType(CommonConstants.unisex);
        queryRequest.setClothImgUrls(clothImgUrls);

        return queryRequest;
    }

    /**
     * 前台用户数据剪枝
     *
     * @param list 数据列表
     * @return 剪枝后数据
     */
    private List<CreativeElementVO> extInfoPruneForFront(List<CreativeElementVO> list) {
        long startTime = System.currentTimeMillis();

        List<CreativeElementVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        list.forEach(e -> {
            CreativeElementVO target = new CreativeElementVO();
            BeanUtils.copyProperties(e, target);
            target.setTags(null);
            if (OperationContextHolder.getRoleType() != RoleTypeEnum.ADMIN) {
                target.setExtInfo(SecurityUtils.clearWithWhiteList(e.getExtInfo(), outputKeyWhitelist));
            } else {
                target.setExtInfo(e.getExtInfo());
            }
            target.setExtTags(null);
            target.setMemo(null);

            if (OperationContextHolder.isBackRole() || target.getStatus() == ElementStatusEnum.PROD) {
                result.add(target);
            }

            if (CollectionUtils.isNotEmpty(e.getChildren())) {
                List<CreativeElementVO> children = extInfoPruneForFront(e.getChildren());
                target.setChildren(children);
            }
        });

        log.info("前台用户数据剪枝，剪枝action,rt={}ms", System.currentTimeMillis() - startTime);

        return result;
    }

    /**
     * 前台用户数据剪枝
     *
     * @param list 数据列表
     * @return 剪枝后数据
     */
    private List<CreativeElementVO> pruneForFront(List<CreativeElementVO> list) {
        long startTime = System.currentTimeMillis();

        List<CreativeElementVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        List<DistributorCustomerVO> distributorCustomers = getDistributorCustomers();

        list.forEach(e -> {
            if (!isOwnOpenScope(e, distributorCustomers)) {
                return;
            }

            CreativeElementVO target = new CreativeElementVO();
            BeanUtils.copyProperties(e, target);
            target.setTags(null);
            target.setExtInfo(SecurityUtils.clearWithWhiteList(e.getExtInfo(), outputKeyWhitelist));
            target.setExtTags(null);
            target.setMemo(null);

            if (OperationContextHolder.isBackRole() || target.getStatus() == ElementStatusEnum.PROD) {
                result.add(target);
            }

            if (CollectionUtils.isNotEmpty(e.getChildren())) {
                List<CreativeElementVO> children = pruneForFront(e.getChildren());
                target.setChildren(children);
            }
        });

        log.info("前台用户数据剪枝，剪枝action,rt={}ms", System.currentTimeMillis() - startTime);

        return result;
    }

    private List<DistributorCustomerVO> getDistributorCustomers() {
        if (!OperationContextHolder.isDistributorRole()) {
            return null;
        }
        Integer currentUserId = OperationContextHolder.getMasterUserId();
        List<DistributorCustomerVO> customers = distributorCustomerCache.getIfPresent(currentUserId);
        if (customers != null) {
            return customers;
        }

        customers = distributorCustomerService.queryDistributorCustomersByCurrentUserAuth(false);
        distributorCustomerCache.put(currentUserId, customers);
        return customers;
    }

    /**
     * 判断当前用户是否有该元素的权限
     *
     * @param element              元素
     * @param distributorCustomers 当前用户拥有的商户列表
     * @return true, 用于权限，false无权限
     */
    private boolean isOwnOpenScope(CreativeElementVO element, List<DistributorCustomerVO> distributorCustomers) {
        // 1.后台人员默认有权限
        if (OperationContextHolder.isBackRole()) {
            return true;
        }

        // 2.只针对专属模特/场景生效，非专属场景直接返回true
        Object openScope = element.getExtInfo(KEY_OPEN_SCOPE);
        if (openScope == null || StringUtils.isBlank(openScope.toString()) || openScope.equals(ALL)) {
            return true;
        }

        // 3.非渠道商场景下，只需要判断当前模特/场景是否归属于当前主账号即可
        if (!OperationContextHolder.isDistributorRole()) {
            if (!openScope.equals(OperationContextHolder.getMasterUserId() + "")) {
                log.info("当前元素属于用户{}专属，直接跳过,current={}", openScope,
                    OperationContextHolder.getMasterUserId());
                return false;
            }

            return true;
        }

        // 4、如果当前账号的 id 为 openScope 的值，则直接返回 true
        if (StringUtils.equals(OperationContextHolder.getMasterUserId().toString(), openScope.toString())) {
            return true;
        }

        // 5、渠道商逻辑
        if (CollectionUtils.isNotEmpty(distributorCustomers)) {
            boolean hasPermission = distributorCustomers.stream().anyMatch(
                t -> StringUtils.equals(t.getCustomerMasterUserId().toString(), openScope.toString()));
            if (!hasPermission) {
                log.info("当前元素属于用户{}专属，且当前渠道商无权限，直接跳过,current={}", openScope,
                    OperationContextHolder.getMasterUserId());
                return false;
            }
        }

        return true;
    }

    /**
     * 从配置中获取jsonArray
     *
     * @param key 关键字
     * @return jsonArray
     */
    private Result<JSONArray> getConfigJsonArray(String key) {
        String cfg = systemConfigService.queryValueByKey(key);
        if (CommonUtil.isValidJsonArray(cfg)) {
            return Result.success(JSONArray.parseArray(cfg));
        } else {
            return Result.success();
        }
    }

    /**
     * 查询最近使用的元素
     *
     * @param elementQuery 元素查询对象
     * @param number       查询数量
     * @param bizType      业务类型枚举
     * @return 元素列表
     */
    private List<CreativeElementVO> queryRecentElements(CreativeElementQuery elementQuery, int number,
                                                        CreativeBizTypeEnum bizType) {
        String key = elementQuery.getKey();
        ElementConfigKeyEnum configKey = StringUtils.isBlank(key) ? ElementConfigKeyEnum.SCENE
            : ElementConfigKeyEnum.valueOf(key);

        CreativeBatchElementsQuery query = new CreativeBatchElementsQuery();
        query.setOperatorId(elementQuery.getUserId());
        query.setElementKey(configKey.name());
        query.setOrderBy("id desc");
        query.setPageSize(number);
        query.setPageNum(1);

        // 初始化includesTypes
        List<String> includesTypes = new ArrayList<>();

        if (bizType != null && CollectionUtils.isNotEmpty(bizType.getIncludesTypes())) {
            includesTypes.addAll(bizType.getIncludesTypes());
            query.setExcludesTypes(bizType.getExcludesTypes());
        }

        // 如果 ageRanges不为空，则将其添加进入setExcludesTypes
        if (CollectionUtils.isNotEmpty(elementQuery.getAgeRanges())) {
            query.setIncludesTypes(elementQuery.getAgeRanges());
        }

        // 如果ageRange不为空，则将其添加进入includesTypes
        if (StringUtils.isNotBlank(elementQuery.getAgeRange())) {
            includesTypes.add(elementQuery.getAgeRange());
        }

        // 如果clothType不为空，则将其添加进入includesTypes
        if (StringUtils.isNotBlank(elementQuery.getClothType())) {
            includesTypes.add(elementQuery.getClothType());
        }

        // 设置包含类型列表
        if (CollectionUtils.isNotEmpty(includesTypes)) {
            query.setIncludesTypes(includesTypes);
        }

        if (StringUtils.isNotBlank(elementQuery.getLabelType())) {
            query.setLabelType(elementQuery.getLabelType());
        }

        if (StringUtils.isNotBlank(elementQuery.getLabelTypeNotEqual())) {
            query.setLabelTypeNotEqual(elementQuery.getLabelTypeNotEqual());
        }

        // 设置是否为lora
        query.setOnlyLora(elementQuery.isOnlyLora());

        return creativeBatchElementsService.queryRecentElements(query);
    }

}
