package ai.conrain.aigc.platform.web.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.component.ComfyuiTaskService;
import ai.conrain.aigc.platform.service.component.ComfyuiWorkflowTemplateService;
import ai.conrain.aigc.platform.service.component.DistributorService;
import ai.conrain.aigc.platform.service.component.ImageCaseService;
import ai.conrain.aigc.platform.service.component.MaterialInfoService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.MerchantPreferenceService;
import ai.conrain.aigc.platform.service.component.PatchTaskService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserProfileService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.component.WorkflowTaskService;
import ai.conrain.aigc.platform.service.component.annotation.Roles;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothTypeConfig;
import ai.conrain.aigc.platform.service.model.biz.ComfyuiTplInfo;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.biz.UserClothMatchingPreference;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.MaterialModelConverter;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.UserProfileQuery;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.query.WorkflowTaskQuery;
import ai.conrain.aigc.platform.service.model.request.AutoGenImgParam;
import ai.conrain.aigc.platform.service.model.request.ConfirmLoraReq;
import ai.conrain.aigc.platform.service.model.request.ReCutoutSingleImageRequest;
import ai.conrain.aigc.platform.service.model.request.ReTrainLoraRequest;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiTaskVO;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateVO;
import ai.conrain.aigc.platform.service.model.vo.LabelFileEditReq;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelReq;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.ModelTrainDetailVO;
import ai.conrain.aigc.platform.service.model.vo.SyncImageCaseReq;
import ai.conrain.aigc.platform.service.model.vo.SystemConfigVO;
import ai.conrain.aigc.platform.service.model.vo.UserProfileVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.model.vo.WorkflowTaskVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.FreemarkerUtils;
import ai.conrain.aigc.platform.service.util.MaterialModelUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.OrderUtils;
import ai.conrain.aigc.platform.service.util.SecurityUtils;
import ai.conrain.aigc.platform.service.util.UserUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_AGE_RANGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_CATEGORY;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_COLOR_IMAGES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_NUM;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_TYPE_CONFIGS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DELIVERY_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_MODIFY_PROMPT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LABEL_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MEMO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_OP_VERSION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RELATED_OPERATOR;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REVIEWER_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_USAGE_MEMO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_USAGE_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_WORK_SCHEDULED_TIME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.TUTORIAL_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.clothStyleType;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.disableOperatorNick;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.disableReason;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.highResModelShowImgUrl;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.SYSTEM_LORA_ORDER_CFG;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.ADMIN;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DISTRIBUTOR;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.MERCHANT;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.OPERATOR;

/**
 * MaterialModel控制器
 *
 * <AUTHOR>
 * @version MaterialModelService.java v 0.1 2024-05-09 02:06:17
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/materialModel")
public class MaterialModelController {
    /** 允许输出的扩展字段白名单 */
    public static final List<String> outputKeyWhitelist = Arrays.asList(clothStyleType, CommonConstants.KEY_CLOTH_MARK,
        disableReason, disableOperatorNick, highResModelShowImgUrl, KEY_AGE_RANGE, KEY_CLOTH_NUM, KEY_CLOTH_CATEGORY,
        KEY_MEMO, TUTORIAL_IMAGE, KEY_USAGE_TYPE, KEY_USAGE_MEMO, KEY_LABEL_TYPE);
    private static final List<String> ADMIN_WHITE_LIST = Arrays.asList("13732280808", "18610655771", "19932600928",
        "18966484977");// 登登实际在后台配置的报警账户是18717831174，要替换成操作员的账号18966484977

    @Autowired
    private MaterialInfoService materialInfoService;

    /** materialModelService */
    @Autowired
    private MaterialModelService materialModelService;

    @Autowired
    private ComfyUIService comfyUIService;

    @Autowired
    private ComfyuiTaskService comfyuiTaskService;

    @Autowired
    private UserService userService;

    @Autowired
    private OssService ossService;

    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private MerchantPreferenceService merchantPreferenceService;
    @Autowired
    private FileDispatch fileDispatch;
    @Autowired
    private ServerHelper serverHelper;

    @Autowired
    private DistributorService distributorService;

    @Autowired
    private ImageCaseService imageCaseService;

    @Autowired
    private WorkflowTaskService workflowTaskService;

    @Autowired
    private UserProfileService userProfileService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ComfyuiWorkflowTemplateService comfyuiWorkflowTemplateService;

    @Autowired
    private PatchTaskService patchTaskService;

    @GetMapping("/getById/{id}")
    public Result<MaterialModelVO> getById(@NotNull @PathVariable("id") Integer id) {
        MaterialModelVO data = materialModelService.selectById(id);

        // 数据不存在直接返回 null
        if (data == null) {
            return Result.success(null);
        }

        if (data.getClothTypeConfigs() == null) {
            data.setClothTypeConfigs(MaterialModelConverter.convert2ClothTypeConfig(data));
        }
        Integer opVersion = data.getExtInfo(KEY_OP_VERSION, Integer.class);
        data.setOpVersion(opVersion != null ? opVersion : 1);

        return Result.success(data);
    }

    @PostMapping("/getTrainDetail")
    public Result<ModelTrainDetailVO> getTrainDetail(@JsonArg @NotNull Integer id) {
        ModelTrainDetailVO model = materialModelService.getTrainDetail(id);
        if (model == null) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "不存在的模型id:" + id);
        }
        WorkflowTaskQuery workflowTaskQuery = new WorkflowTaskQuery();
        workflowTaskQuery.setBizId(model.getId());
        List<WorkflowTaskVO> tasks = workflowTaskService.queryWorkflowTaskList(workflowTaskQuery);
        model.setTasks(tasks);
        return Result.success(model);
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody MaterialModelVO materialModel) {
        try {
            MaterialModelVO data = materialModelService.insert(materialModel);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加素材模型失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建素材模型失败");
    }

    @PostMapping("/cloneLora")
    public Result<?> cloneLora(@JsonArg @NotNull Integer modelId, @JsonArg Boolean fullCopy) {
        MaterialModelVO newLora = materialModelService.cloneLora(modelId, null, null,
            fullCopy != null ? fullCopy : false);
        return Result.success(newLora.getId());
    }

    @PostMapping("/copyToSystem")
    public Result<?> copyToSystem(@JsonArg @NotNull Integer modelId) {
        MaterialModelVO newLora = materialModelService.copyToSystem(modelId);
        return Result.success(newLora.getId());
    }

    @PostMapping("/retrainLora")
    public Result<?> retrainLora(@JsonArg @NotNull Integer modelId, @JsonArg String labelType,
                                 @JsonArg String cut4ScaleUp, @JsonArg String preprocessCensoredFace) {
        ReTrainLoraRequest request = new ReTrainLoraRequest();
        request.setId(modelId);
        request.setAutoTrain(true);
        request.setLabelType(labelType);
        request.setCut4ScaleUp(cut4ScaleUp);
        request.setPreprocessCensoredFace(preprocessCensoredFace);
        request.setPrepareViewAgainWhenCutoutAgain(true);

        materialModelService.retrainLora(request);
        return Result.success();
    }

    @PostMapping("/relabelLora")
    public Result<?> relabelLora(@JsonArg @NotNull Integer modelId, @JsonArg String labelType,
                                 @JsonArg String cut4ScaleUp, @JsonArg String preprocessCensoredFace) {
        ReTrainLoraRequest request = new ReTrainLoraRequest();
        request.setId(modelId);
        request.setAutoTrain(false);
        request.setLabelType(labelType);
        request.setCut4ScaleUp(cut4ScaleUp);
        request.setPreprocessCensoredFace(preprocessCensoredFace);
        request.setPrepareViewAgainWhenCutoutAgain(true);

        materialModelService.retrainLora(request);
        return Result.success();
    }

    @PostMapping("/add/system")
    public Result<Integer> addSystem(@Valid @RequestBody MaterialModelVO materialModel) {
        materialModel.setType(ModelTypeEnum.SYSTEM);
        materialModel.setOperatorId(OperationContextHolder.getOperatorUserId());
        AssertUtil.assertTrue(StringUtils.isNotBlank(materialModel.getShowImage()), ResultCode.PARAM_INVALID,
            "图片未上传");
        try {
            MaterialModelVO data = materialModelService.insert(materialModel);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加官方素材模型失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建素材模型失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        materialModelService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody MaterialModelVO materialModel) {
        MaterialModelVO current = materialModelService.selectById(materialModel.getId());
        AssertUtil.assertNotNull(current, "模型不存在");

        // bugfix：将新旧两个ext info进行merge，避免丢失key
        JSONObject extInfo = current.getExtInfo() != null ? current.getExtInfo() : new JSONObject();

        // 由其它状态改为审核不通过状态时，记录操作信息到扩展字段
        if (!MaterialModelStatusEnum.DISABLED.getCode().equals(current.getStatus())
            && MaterialModelStatusEnum.DISABLED.getCode().equals(materialModel.getStatus())) {
            log.info("模型被禁用modelId={}", materialModel.getId());
            extInfo.put(CommonConstants.disableOperatorId, OperationContextHolder.getOperatorUserId().toString());
            extInfo.put(CommonConstants.disableOperatorNick, OperationContextHolder.getOperatorNick());
            extInfo.put(CommonConstants.disableTime, DateUtils.formatTime(new Date()));
        }

        if (materialModel.getExtInfo() != null) {
            extInfo.putAll(materialModel.getExtInfo());
        }

        materialModel.setExtInfo(extInfo);

        if (CollectionUtils.isNotEmpty(materialModel.getClothTypeConfigs())) {
            materialModel.addExtInfo(KEY_CLOTH_TYPE_CONFIGS, materialModel.getClothTypeConfigs());
            if (MaterialModelUtils.isModifyPrompt(current, materialModel)) {
                materialModel.addExtInfo(KEY_IS_MODIFY_PROMPT, YES);
            }
        }

        if (!MaterialModelStatusEnum.ENABLED.getCode().equals(current.getStatus())
            && MaterialModelStatusEnum.ENABLED.getCode().equals(materialModel.getStatus())) {
            log.info("模型被启用modelId={},operator={}", materialModel.getId(),
                OperationContextHolder.getOperatorUserId());
            materialModel.addExtInfo(KEY_DELIVERY_OPERATOR, OperationContextHolder.getOperatorUserId());
        }

        if (materialModel.getExtInfo(KEY_RELATED_OPERATOR, Integer.class) == null
            && OperationContextHolder.isBackRole()) {
            materialModel.addExtInfo(KEY_RELATED_OPERATOR, OperationContextHolder.getOperatorUserId());
        }

        if (materialModel.getClothLoraTrainDetail() != null) {
            AutoGenImgParam targetParam = materialModel.getClothLoraTrainDetail().getAutoGenImgParam();
            if (targetParam != null) {
                log.info("更新训练详情，只变更自动生成图片参数id={},targetParam={}", materialModel.getId(), targetParam);
                // 自动生图的模型，审核不通过后需要设置自动生图为false，去除自动生图权限，防止重新审核通过时不知道是否需要再次生图
                if (materialModel.getClothLoraTrainDetail().getAutoGenImg().equals(false) && materialModel.getStatus().equals("DISABLED")) {
                    current.getClothLoraTrainDetail().setAutoGenImg(false);
                }
                materialModel.setClothLoraTrainDetail(current.getClothLoraTrainDetail());
                materialModel.getClothLoraTrainDetail().setAutoGenImgParam(targetParam);
            } else {
                materialModel.setClothLoraTrainDetail(null);
            }
        }

        materialModelService.updateByIdSelective(materialModel);
        return Result.success();
    }

    @PostMapping("/deliver")
    public Result<?> deliver(@Valid @RequestBody MaterialModelVO materialModel) {
        materialModelService.deliver(materialModel);
        return Result.success();
    }

    @PostMapping("/addLora2Vip")
    public Result<?> add2LoraVip(@JsonArg @NotNull Integer loraId) {
        AssertUtil.assertTrue(OperationContextHolder.isAdmin(), "非管理员不能操作");
        MaterialModelVO model = materialModelService.selectById(loraId);
        if (model == null) {
            return Result.error("lora不存在");
        }

        SystemConfigVO loraVipCfg = systemConfigService.queryByKey(SystemConstants.LORA_VIP);
        AssertUtil.assertNotNull(loraVipCfg, "LORA_VIP配置不存在");

        JSONArray loraVip = JSONArray.parseArray(loraVipCfg.getConfValue());
        if (loraVip == null) {
            loraVip = new JSONArray();
        }
        if (!loraVip.contains(loraId)) {
            loraVip.add(loraId);
        }

        if (model.getMainType() == MainTypeEnum.MAIN) {
            List<MaterialModelVO> subList = materialModelService.querySubModel(loraId);

            if (CollectionUtils.isNotEmpty(subList)) {
                loraVip.addAll(subList.stream().map(MaterialModelVO::getId).collect(Collectors.toList()));
            }
        }

        // Keep only the latest 200 IDs
        if (loraVip.size() > 200) {
            loraVip = new JSONArray(loraVip.subList(loraVip.size() - 200, loraVip.size()));
        }

        loraVipCfg.setConfValue(loraVip.toJSONString());
        systemConfigService.updateById(loraVipCfg);

        return Result.success();
    }

    @PostMapping("/queryAllModelCreators")
    public Result<List<UserVO>> queryAllModelCreators() {
        MaterialModelQuery query = new MaterialModelQuery();
        query.setUserId(OperationContextHolder.getMasterUserId());

        List<MaterialModelVO> models = materialModelService.queryMaterialModelList(query);
        if (models != null) {
            Map<Integer, UserVO> map = Maps.newHashMap();
            for (MaterialModelVO model : models) {
                AssertUtil.assertNotNull(model.getOperatorId(), "模型的操作人id为空");
                if (!map.containsKey(model.getOperatorId())) {
                    UserVO u = new UserVO();
                    u.setId(model.getOperatorId());
                    u.setNickName(model.getOperatorNick());

                    map.put(model.getOperatorId(), u);
                }
            }

            return Result.success(new ArrayList<>(map.values()));
        }

        return Result.success();
    }

    @PostMapping("/querySystemList")
    public Result<PageInfo<MaterialModelVO>> querySystemList(@Valid @RequestBody MaterialModelQuery query) {
        Integer originPageSize = query.getPageSize();
        query.setType(ModelTypeEnum.SYSTEM.getCode());
        query.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);
        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }

        if (OperationContextHolder.isBackRole() && StringUtils.isNotBlank(query.getStatus())) {
            query.setStatusList(Lists.newArrayList(query.getStatus(), MaterialModelStatusEnum.TESTING.getCode()));
            query.setStatus(null);
        }
        // TODO by半泉:临时代码，对体验服装进行排序
        JSONArray array = systemConfigService.queryJsonArrValue(SYSTEM_LORA_ORDER_CFG);
        boolean needOrder = array != null && !array.isEmpty();

        if (needOrder) {
            query.setPageSize(20);
        }

        PageInfo<MaterialModelVO> pageInfo = materialModelService.queryMaterialModelByPage(query);
        List<MaterialModelVO> list = pageInfo.getList();
        if (needOrder && CollectionUtils.isNotEmpty(list)) {
            OrderUtils.sortListByJsonArray(list, array);
            pageInfo.setHasNextPage(list.size() > originPageSize);

            list = list.subList(0, originPageSize > list.size() ? list.size() : originPageSize);
            query.setPageSize(originPageSize);
            pageInfo.setSize(originPageSize);
        }
        pageInfo.setList(pruneForFront(list));

        return Result.success(pageInfo);
    }

    @PostMapping("/queryList")
    public Result<List<MaterialModelVO>> queryMaterialModelList(@Valid @RequestBody MaterialModelQuery query) {
        if (!OperationContextHolder.isBackRole()) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        }
        query.setType(ModelTypeEnum.CUSTOM.getCode());
        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }
        return Result.success(materialModelService.queryMaterialModelList(query));
    }

    @PostMapping("/queryListWithBlogs")
    public Result<PageInfo<MaterialModelVO>> queryListWithBlogs(@Valid @RequestBody MaterialModelQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        if (!OperationContextHolder.isBackRole()) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        }
        if (StringUtils.isBlank(query.getOrderBy())) {
            String recentCreate = "create_time > '" + DateUtils.formatTime(DateUtils.addWeeks(new Date(), -1)) + "'";
            // 优先逾期包括20个小时未处理 > 销售 > 客户
            query.setOrderBy("if(status not in ('ENABLED','DISABLED') and create_time < '" + DateUtils.formatTime(
                DateUtils.addHours(new Date(), -20)) + "' and " + recentCreate
                             + " and user_role not in ('ADMIN', 'OPERATOR'),1,0) desc, "
                             + "if(status not in ('ENABLED','DISABLED') and user_role = 'DISTRIBUTOR' and "
                             + recentCreate + ",1,0) desc,id desc");
        }

        if (query.getDistributorMasterId() != null) {
            List<UserVO> customers = distributorService.queryAllCustomersByDistributorMasterId(
                query.getDistributorMasterId());
            if (CollectionUtils.isNotEmpty(customers)) {
                query.setUserIds(customers.stream().map(UserVO::getId).collect(Collectors.toList()));
            } else {
                query.setUserIds(new ArrayList<>());
            }
        }

        fillRelatedToMeSearchCondition(query);

        query.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);

        // 临时兼容10张精选图的用户
        PageInfo<MaterialModelVO> pageInfo = materialModelService.queryPageWithBlobs(query);
        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            WorkflowTaskQuery workflowTaskQuery = new WorkflowTaskQuery();
            workflowTaskQuery.setBizIds(
                pageInfo.getList().stream().map(MaterialModelVO::getId).collect(Collectors.toList()));
            List<WorkflowTaskVO> tasks = workflowTaskService.queryWorkflowTaskList(workflowTaskQuery);
            String value = systemConfigService.queryValueByKey(SystemConstants.TEN_EXAMPLE_IMAGES_USERS);
            if (StringUtils.isNotBlank(value)) {
                List<Integer> userIds = JSONArray.parseArray(value, Integer.class);
                pageInfo.getList().forEach(model -> {
                    if (userIds.contains(model.getUserId())) {
                        model.addExtInfo("TEN_EXAMPLE_IMAGES", true);
                    }
                });
            }

            // 兼容处理
            pageInfo.getList().forEach(model -> {
                if (model.getClothTypeConfigs() == null) {
                    model.setClothTypeConfigs(MaterialModelConverter.convert2ClothTypeConfig(model));
                }
                List<WorkflowTaskVO> filteredTasks = tasks.stream().filter(
                    task -> task.getBizId().equals(model.getId())).collect(Collectors.toList());
                model.setTasks(filteredTasks);
            });
        }
        return Result.success(pageInfo);
    }

    /**
     * 前台用户查询「全部资产」
     *
     * @param query
     * @return
     */
    @PostMapping("/queryByPage")
    public Result<PageInfo<MaterialModelVO>> getMaterialModelByPage(@Valid @RequestBody MaterialModelQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        if (StringUtils.isBlank(query.getOrderBy())) {
            if (OperationContextHolder.isBackRole()) {
                query.setOrderBy(
                    "if(id in (select model_id from (select model_id from (select model_id,max(id) id from "
                    + "creative_batch where user_id = " + OperationContextHolder.getOperatorUserId()
                    + " and deleted != 1 group by model_id) t order by id desc " + "limit 4) s) , 1, 0) desc, id desc");
            } else {
                query.setOrderBy("id desc");
            }
        }

        // 演示账号
        if (OperationContextHolder.getRoleType() == RoleTypeEnum.DEMO_ACCOUNT) {
            query.setOnlyShowDemo(true);
            query.setIsOwner(null);
        } else if (!OperationContextHolder.isBackRole() && !systemConfigService.isInJsonArray(
            SystemConstants.SEE_ALL_MODELS_AND_HISTORY, OperationContextHolder.getOperatorUserId())) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        } else {
            if (StringUtils.isNotBlank(query.getStatus())) {
                query.setStatusList(Lists.newArrayList(query.getStatus(), MaterialModelStatusEnum.TESTING.getCode()));
                query.setStatus(null);
            }

            String operatorMobile = OperationContextHolder.getContext().getOperationSession().getLoginUser()
                .getMobile();

            // 后台用户《我的服装》，同时返回跟进操作员的服装
            if (query.getIsOwner() != null && query.getIsOwner() && ADMIN_WHITE_LIST.contains(operatorMobile)) {
                query.setIsOwner(null);
                query.setRelatedOperatorType(
                    StringUtils.equals("18966484977", operatorMobile) ? "18717831174" : operatorMobile);

                query.setRelatedOrOperatorId(OperationContextHolder.getOperatorUserId());
            }
        }

        query.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);

        // 如果客户角色是商家并且模型类型是face并且是test状态并且是实验模型 就不给他显示
        if (OperationContextHolder.getRoleType() == RoleTypeEnum.MERCHANT && (query.getHideTestExperimental() != null && query.getHideTestExperimental().equals(true)) && query.getMaterialType().equals("face")) {
            query.setHideTestExperimental(true);
        }
        PageInfo<MaterialModelVO> pageInfo = materialModelService.queryMaterialModelByPage(query);
        pageInfo.setList(pruneForFront(pageInfo.getList()));

        return Result.success(pageInfo);
    }

    @PostMapping("/confirmTrainLora")
    public Result<?> confirmTrainLora(@RequestBody @Valid ConfirmLoraReq req) {
        materialModelService.confirmTrainLora(req, OperationContextHolder.getOperatorUserId(),
            OperationContextHolder.getOperatorNick());
        return Result.success();
    }

    @PostMapping("/confirmCanDeliver")
    public Result<?> confirmCanDeliver(@JsonArg @NotNull Integer loraId) {
        materialModelService.confirmCanDeliver(loraId, OperationContextHolder.getOperatorUserId(),
            OperationContextHolder.getOperatorNick());
        return Result.success();
    }

    /**
     * 重新抠图打标
     */
    @PostMapping("/cutoutAgain")
    public Result<?> cutoutAgain(@RequestBody @Valid ReTrainLoraRequest request) {
        materialModelService.retrainLora(request);
        return Result.success();
    }

    /**
     * 为服装训练指定平台运营
     */
    @PostMapping("/assignPlatformOperator")
    public Result<?> assignPlatformOperator(@NotNull @JsonArg Integer id, @NotNull @JsonArg String operatorMobile) {
        MaterialModelVO model = materialModelService.selectById(id);
        if (model != null) {
            MaterialModelVO target = new MaterialModelVO();
            target.setId(model.getId());
            target.setExtInfo(model.getExtInfo());

            LoraTrainDetail trainDetail = model.getClothLoraTrainDetail();

            UserQuery query = new UserQuery();
            query.setMobile(operatorMobile);
            List<UserVO> users = userService.queryUsers(query);
            if (CollectionUtils.isEmpty(users)) {
                return Result.failedWithMessage(ResultCode.BIZ_FAIL, "平台运营不存在");
            }

            trainDetail.setRelatedOperatorId(users.get(0).getId());
            trainDetail.setRelatedOperatorMobile(users.get(0).getMobile());
            trainDetail.setRelatedOperatorNick(users.get(0).getNickName());
            target.setClothLoraTrainDetail(trainDetail);

            materialModelService.updateByIdSelective(target);
        }

        return Result.success();
    }

    @PostMapping("/updateLabelFiles")
    public Result<Boolean> updateLabelFiles(@Valid @RequestBody LabelFileEditReq req) {

        materialModelService.updateLabelFiles(req);

        return Result.success(true);
    }

    /**
     * 更新服装模型扩展信息(该接口数据处理不影响正常业务)
     *
     * @param req 请求入参
     * @return 是否更新成功
     */
    @PostMapping("/updateExtInfo")
    public Result<Boolean> updateExtInfo(@Valid @RequestBody MaterialModelReq req) {
        // 更新扩展信息
        materialModelService.updateExtInfo(req);

        // 返回成功
        return Result.success();
    }

    /**
     * 更新审核员
     *
     * @return 是否更新成功
     */
    @PostMapping("/updateReviewer")
    public Result<Boolean> updateExtInfo(@JsonArg Integer id, @JsonArg Integer reviewerId) {
        MaterialModelVO materialModelVO = materialModelService.selectById(id);
        materialModelVO.addExtInfo(KEY_REVIEWER_ID, reviewerId);
        materialModelVO.addExtInfo(KEY_WORK_SCHEDULED_TIME, new Date());

        materialModelService.updateExtInfoByIdSelective(materialModelVO);
        return Result.success();
    }

    /**
     * 同步服装模型到图库中
     *
     * @param req 请求入参
     * @return 是否更新成功
     */
    @PostMapping("/syncToImageCase")
    public Result<Boolean> syncToImageCase(@Valid @RequestBody SyncImageCaseReq req) {
        // 同步服装模型到图库中
        materialModelService.syncToImageCase(req);

        // 返回成功
        return Result.success();
    }

    @PostMapping("/assignTo")
    public Result<?> assignTo(@NotNull @JsonArg Integer modelId, @NotNull @JsonArg Integer userId) {
        materialModelService.assignTo(modelId, userId);
        return Result.success();
    }

    /**
     * 将模特或场景lora，转移给指定客户
     */
    @PostMapping("/assignElementModelToUser")
    public Result<?> assignElementModelToUser(@NotNull @JsonArg Integer modelId, @NotNull @JsonArg Integer userId,
                                              @NotNull @JsonArg Boolean exclusive, @NotNull @JsonArg Boolean free) {
        materialModelService.assignElementModelToUser(modelId, userId, exclusive, free);
        return Result.success();
    }

    @PostMapping("/changeExampleImages")
    public Result<?> changeExampleImages(@Valid @RequestBody MaterialModelVO materialModel) {
        AssertUtil.assertNotNull(materialModel, "数据不能为空");
        AssertUtil.assertNotNull(materialModel.getId(), "id不能为空");
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(materialModel.getExampleImages()), "精选图不能为空");

        materialModelService.changeExampleImages(materialModel);
        return Result.success();
    }

    @PostMapping("/clearExampleImages")
    public Result<?> clearExampleImages(@NotNull @JsonArg Integer modelId) {
        materialModelService.clearExampleImages(modelId);
        return Result.success();
    }

    @PostMapping("/markCloth")
    public Result<?> markCloth(@JsonArg @NotNull Integer modelId, @JsonArg @NotBlank String markKey,
                               @JsonArg @NotBlank String markValue) {
        MaterialModelVO modelVO = materialModelService.selectById(modelId);
        if (modelVO == null) {
            return Result.failedWithMessage(ResultCode.BIZ_FAIL, "服装模型不存在");
        }
        JSONObject clothMark = modelVO.getExtInfo().getJSONObject(CommonConstants.KEY_CLOTH_MARK);
        if (clothMark == null) {
            clothMark = new JSONObject();
        }
        clothMark.put(markKey, markValue);
        modelVO.addExtInfo(CommonConstants.KEY_CLOTH_MARK, clothMark.toJSONString());
        materialModelService.updateByIdSelective(modelVO);
        return Result.success();
    }

    @PostMapping("/queryDetailShowImage")
    public Result<?> queryDetailShowImage(@NotNull @JsonArg Integer modelId) {
        return Result.success(materialModelService.queryDetailShowImage(modelId));
    }

    @PostMapping("/queryDetailShowImages")
    public Result<?> queryDetailShowImages(@NotNull @JsonArg Integer modelId) {
        return Result.success(materialModelService.queryDetailShowImages(modelId));
    }

    @PostMapping("/updateColorImage")
    public Result<?> updateColorImage(@NotNull @JsonArg Integer id, @NotNull @JsonArg Integer index,
                                      @NotNull @JsonArg String imgUrl) {
        materialModelService.updateColorImage(id, index, imgUrl);
        return Result.success();
    }

    @PostMapping("/enableColor")
    public Result<?> enableColor(@NotNull @JsonArg Integer id, @NotNull @JsonArg Integer index,
                                 @NotNull @JsonArg Boolean enable) {
        materialModelService.enableColor(id, index, enable);
        return Result.success();
    }

    @PostMapping("/batchModifyGarment")
    public Result<?> batchModifyGarment(@NotNull @JsonArg String ids) {
        // TODO by半泉:临时批量订正使用
        materialModelService.batchModifyGarment(Arrays.stream(ids.split(",")).map(Integer::parseInt)
            .collect(Collectors.toList()));
        return Result.success();
    }

    @PostMapping("/supplyColor")
    public Result<?> supplyColor(@NotNull @JsonArg String ids) {
        // TODO by半泉:临时批量订正使用
        materialModelService.supplyColor(Arrays.stream(ids.split(",")).map(Integer::parseInt)
            .collect(Collectors.toList()));
        return Result.success();
    }

    @PostMapping("/batchUploadOss")
    public Result<?> batchUploadOss(@NotNull @JsonArg String ids) {
        materialModelService.batchUploadOss(Arrays.stream(ids.split(",")).map(Integer::parseInt)
            .collect(Collectors.toList()));
        return Result.success();
    }

    @PostMapping("/batchCreateTestImages")
    public Result<?> batchCreateTestImages(@NotNull @JsonArg String ids) {
        materialModelService.batchCreateTestImages(Arrays.stream(ids.split(",")).map(Integer::parseInt)
            .collect(Collectors.toList()));
        return Result.success();
    }

    @PostMapping("/batchRelabelLora")
    public Result<?> batchRelabelLora(@NotNull @JsonArg String ids) {
        String[] split = ids.split(",");
        for (String s : split) {
            ReTrainLoraRequest request = new ReTrainLoraRequest();
            request.setId(Integer.parseInt(s));
            request.setAutoTrain(false);
            request.setPrepareViewAgainWhenCutoutAgain(true);

            materialModelService.retrainLora(request);
        }
        return Result.success();
    }

    @PostMapping("/batchRetrainLora")
    public Result<?> batchRetrainLora(@NotNull @JsonArg String ids) {
        String[] split = ids.split(",");
        for (String s : split) {
            ReTrainLoraRequest request = new ReTrainLoraRequest();
            request.setId(Integer.parseInt(s));
            request.setAutoTrain(true);
            request.setPrepareViewAgainWhenCutoutAgain(true);

            materialModelService.retrainLora(request);
        }
        return Result.success();
    }

    @PostMapping("/batchRetrainAndAutoComplete")
    public Result<?> batchRetrainAndAutoComplete(@NotNull @JsonArg String ids) {
        String[] split = ids.split(",");
        for (String s : split) {
            ReTrainLoraRequest request = new ReTrainLoraRequest();
            request.setId(Integer.parseInt(s));
            request.setAutoTrain(true);
            request.setAutoComplete(true);
            request.setPrepareViewAgainWhenCutoutAgain(true);

            materialModelService.retrainLora(request);
        }
        return Result.success();
    }

    @PostMapping("/batchInitTrainedModel")
    public Result<?> batchInitTrainedModel(@NotNull @JsonArg String ids) {
        String[] split = ids.split(",");
        for (String s : split) {
            materialModelService.reInitTrainedModel(Integer.parseInt(s));
        }
        return Result.success();
    }

    @PostMapping("/batchAutoCreateImages")
    public Result<?> batchAutoCreateImages(@NotNull @JsonArg String ids) {
        String[] split = ids.split(",");
        for (String s : split) {
            materialModelService.autoCreateImages(Integer.parseInt(s));
        }
        return Result.success();
    }

    @PostMapping("/batchTrainFolderSync")
    public Result<?> batchTrainFolderSync(@NotNull @JsonArg String ids) {
        String[] split = ids.split(",");
        for (String s : split) {
            materialModelService.syncTrainFolder(Integer.parseInt(s));
        }
        return Result.success();
    }

    @PostMapping("/batchAutoDelivery")
    public Result<?> batchAutoDelivery(@NotNull @JsonArg String ids) {
        String[] split = ids.split(",");
        for (String s : split) {
            materialModelService.batchAutoDelivery(Integer.parseInt(s));
        }
        return Result.success();
    }

    @PostMapping("/modifyModelName")
    public Result<?> modifyModelName(@NotNull @JsonArg Integer id, @NotNull @JsonArg String name) {
        MaterialModelVO model = new MaterialModelVO();
        model.setId(id);
        model.setName(name);
        materialModelService.innerUpdate(model);
        return Result.success();
    }

    @PostMapping("/addDemoTag")
    public Result<?> addDemoTag(@JsonArg @NotNull Integer id) {
        materialModelService.addDemoTag(id);
        return Result.success();
    }

    @PostMapping("/updateExperimental")
    public Result<?> updateExperimental(@JsonArg Integer id, @JsonArg boolean experimental) {
        materialModelService.updateExperimental(id, experimental);
        return Result.success();
    }

    @PostMapping("/querySubIds")
    public Result<?> querySubIds(@JsonArg Integer id) {
        return Result.success(materialModelService.querySubIds(id));
    }

    @GetMapping("/statsQueuedModel")
    public Result<?> statsQueuedModel() {
        return Result.success(materialModelService.statsQueuedModel());
    }

    @PostMapping("/fetchWorkflowByComfyuiTaskId")
    public Result<String> fetchWorkflowByComfyuiTaskId(@NotNull @JsonArg Integer taskId)
        throws JsonProcessingException {
        ComfyuiTaskVO task = comfyuiTaskService.selectById(taskId);
        String prompt = composePromptByTpl(task);
        AssertUtil.assertNotBlank(prompt, ResultCode.BIZ_FAIL, "prompt为空，这个任务没有工作流");

        log.info("fetchWorkflowByComfyuiTaskId taskId: {}, prompt: {}", taskId, prompt);

        JSONObject json = JSONObject.parseObject(prompt);
        JSONObject workflow = json.getJSONObject("extra_data").getJSONObject("extra_pnginfo").getJSONObject("workflow");

        ObjectMapper localObjectMapper = objectMapper.copy();
        localObjectMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        return Result.success(localObjectMapper.writeValueAsString(workflow));
    }

    @PostMapping("/addProblemTag")
    public Result<String> addProblemTag(@NotNull @JsonArg Integer id, @NotNull @JsonArg Integer tagId) {
        materialModelService.addProblemTag(id, tagId);
        return Result.success();
    }

    @PostMapping("/forceToDelivery")
    public Result<String> forceToDelivery(@NotNull @JsonArg Integer id) {
        materialModelService.forceToDelivery(id);
        return Result.success();
    }

    @PostMapping("/modifyUsage")
    @Roles({ADMIN, OPERATOR, MERCHANT, DISTRIBUTOR})
    public Result<String> modifyUsage(@NotNull @JsonArg Integer id, @NotBlank @JsonArg String usageType,
                                      @JsonArg String usageMemo) {
        if (StringUtils.equals("experience", usageType) && StringUtils.isBlank(usageMemo)) {
            log.warn("修改用途失败,用途为客户体验时，客户名称为空");
            return Result.failedWithMessage(ResultCode.ILLEGAL_USAGE_INFO, "用途为客户体验时，客户名称为空");
        }
        materialModelService.modifyUsage(id, usageType, usageMemo);
        return Result.success();
    }

    private String composePromptByTpl(ComfyuiTaskVO task) {
        if (task.getComfyuiRequest() != null && CommonUtil.isValidJson(task.getComfyuiRequest())) {
            ComfyuiTplInfo tplInfo = CommonUtil.parseObject(task.getComfyuiRequest(), ComfyuiTplInfo.class);
            if (tplInfo != null && tplInfo.getTplKey() != null) {
                ComfyuiWorkflowTemplateVO template = comfyuiWorkflowTemplateService.queryTemplateByKeyAndVersion(
                    tplInfo.getTplKey(), tplInfo.getTplVersion());
                if (template != null && template.getTemplateData() != null) {
                    Map<String, Object> params = tplInfo.getTplParams();
                    return FreemarkerUtils.parse(template.getTemplateData(), params);
                }
            }
        }
        return null;
    }

    /**
     * 前台用户数据剪枝
     *
     * @param list 数据列表
     * @return 剪枝后数据
     */
    @SuppressWarnings({"unchecked"})
    public static <T extends MaterialModelVO> List<T> pruneForFront(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        list.forEach(e -> {
            String userPreferFeaturesStr = e.getExtInfo(CommonConstants.userPreferFeatures, String.class);
            if (StringUtils.isNotBlank(userPreferFeaturesStr)) {
                UserClothMatchingPreference preference = JSONObject.parseObject(userPreferFeaturesStr,
                    UserClothMatchingPreference.class);
                e.setClothCollocation(preference != null ? preference.getOriginClothCollocation() : null);
            }
            if (e.getClothLoraTrainDetail() != null && e.getClothLoraTrainDetail().getOriginalMaterialId() != null) {
                e.setMaterialInfoId(e.getClothLoraTrainDetail().getOriginalMaterialId());
            }

            e.setMaterialType(null);

            List<ClothTypeConfig> clothTypeConfigs = e.getClothTypeConfigs() != null ? e.getClothTypeConfigs()
                : MaterialModelConverter.convert2ClothTypeConfig(e);
            List<String> colorImages = e.getExtInfo(KEY_CLOTH_COLOR_IMAGES, List.class);

            fillClothTypeConfigInfo(e, clothTypeConfigs, colorImages);

            e.setTags(null);
            e.setClothLoraTrainDetail(null);
            e.setExtInfo(SecurityUtils.clearWithWhiteList(e.getExtInfo(), outputKeyWhitelist));

            if (!(UserUtils.isVirtualMerchant(OperationContextHolder.getCurrentUser())
                  || OperationContextHolder.isDistributorRole()) && MapUtils.isNotEmpty(e.getExtInfo())) {
                e.getExtInfo().remove(KEY_USAGE_TYPE);
                e.getExtInfo().remove(KEY_USAGE_MEMO);
            }

            e.setLoraName(null);
            // bugfix: 【这几个字段需要保留】，在前端的「全部资产」，资产详情页展示时需要使用，因此注释掉这两行
            // e.setMaterialInfoId(null);
            // e.setOperatorNick(null);
            // e.setUserId(null);
            e.setOperatorId(null);
            if (!OperationContextHolder.isDistributorRole()) {
                e.setUserNick(null);
            }
        });

        return list;
    }

    /**
     * 填充服装类型配置信息
     *
     * @param model            模型
     * @param clothTypeConfigs 服装类型配置
     * @param colorImages      颜色图片列表
     */
    private static void fillClothTypeConfigInfo(MaterialModelVO model, List<ClothTypeConfig> clothTypeConfigs,
                                                List<String> colorImages) {
        // 设置颜色列表
        if (CollectionUtils.isEmpty(clothTypeConfigs)) {
            return;
        }

        List<ClothTypeConfig> result = new ArrayList<>();
        for (ClothTypeConfig each : clothTypeConfigs) {
            List<ClothColorDetail> filter = each.getColorList().stream().filter(ClothColorDetail::isEnable).collect(
                Collectors.toList());
            if (CollectionUtils.isEmpty(filter)) {
                continue;
            }

            ClothTypeConfig config = new ClothTypeConfig();
            config.setType(each.getType());

            List<ClothColorDetail> colorList = new ArrayList<>();

            filter.forEach(color -> {
                ClothColorDetail detail = new ClothColorDetail(color.getIndex(), null, null);

                int index = detail.getIndex();
                if (CollectionUtils.isNotEmpty(colorImages) && index <= CollectionUtils.size(colorImages)) {
                    detail.setShowImg(colorImages.get(index - 1));
                }
                colorList.add(detail);
            });

            config.setColorList(colorList);

            result.add(config);
        }
        model.setClothTypeConfigs(result);

        for (ClothTypeConfig clothTypeConfig : clothTypeConfigs) {
            // 在2024-10-26 13:00:00之前创建的模型，默认只支持全身
            if (!OperationContextHolder.isBackRole() && !DateUtils.after(
                Objects.requireNonNull(DateUtils.parseSimple("2024-10-23 14:00:00")), model.getCreateTime(), 0)) {
                break;
            }

            CameraAngleEnum bodyPosition = CameraAngleEnum.getBodyPositionByStr(clothTypeConfig.getType());
            if (StringUtils.isBlank(model.getHalfBody()) && bodyPosition != CameraAngleEnum.WHOLE_BODY) {
                model.setHalfBody(bodyPosition.getCode());
            }
        }
    }

    /**
     * 审核员和工程师筛选项
     *
     * @param query 查询条件
     */
    private void fillRelatedToMeSearchCondition(MaterialModelQuery query) {
        if (query.isRelatedToMe()) {
            query.setReviewerId(OperationContextHolder.getOperatorUserId());
            query.setPromptUserId(OperationContextHolder.getOperatorUserId());
        }
        if (query.getPromptUserId() != null) {
            // 筛选工程师是当前用户的，工程师绑定客户，根据客户筛选
            UserProfileQuery userProfileQuery = new UserProfileQuery();
            userProfileQuery.setProfileKey(CommonConstants.KEY_PROMPT_USER_ID);
            userProfileQuery.setProfileVal(query.getPromptUserId().toString());
            List<UserProfileVO> profiles = userProfileService.queryUserProfileList(userProfileQuery);
            List<Integer> userIds = profiles.stream().map(UserProfileVO::getUid).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userIds)) {
                query.setId(0);
            } else {
                query.setUserIds(userIds);
            }
        }
    }

    @PostMapping("/createRecutPatchTask")
    public Result<?> createRecutPatchTask(@Validated @RequestBody ReCutoutSingleImageRequest request) {
        patchTaskService.createRecutPatchTask(request);
        return Result.success();
    }

    @GetMapping("/getPatchCutoutTasksList/{id}")
    public Result<List<Map<String, Object>>> getPatchCutoutTasksList(@PathVariable("id") Integer id) {
        List<Map<String, Object>> patchCutoutTasksList = patchTaskService.getPatchCutoutTasksList(id);
        return Result.success(patchCutoutTasksList);
    }

    @PostMapping("/deletePatchCutoutTask")
    public Result<?> deletePatchCutoutTask(@RequestParam("modelId") Integer modelId,
                                           @RequestParam("taskId") String taskId) {
        Boolean result = patchTaskService.deletePatchCutoutTask(modelId, taskId);
        if (result) {
            return Result.success();
        } else {
            return Result.error("删除补丁任务失败！");
        }
    }
}
