package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.GalleryService;
import ai.conrain.aigc.platform.service.enums.GallerySubTypeEnum;
import ai.conrain.aigc.platform.service.enums.GalleryTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.GalleryQuery;
import ai.conrain.aigc.platform.service.model.request.GalleryUploadRequest;
import ai.conrain.aigc.platform.service.model.vo.GalleryVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Gallery控制器
 *
 * <AUTHOR>
 * @version GalleryService.java v 0.1 2025-08-20 02:49:20
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/gallery")
public class GalleryController {

    /**
     * galleryService
     */
    @Autowired
    private GalleryService galleryService;

    @GetMapping("/getById/{id}")
    public Result<GalleryVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(galleryService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody GalleryVO gallery) {
        return Result.success(galleryService.insert(gallery).getId());
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        galleryService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody GalleryVO gallery) {
        galleryService.updateByIdSelective(gallery);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<GalleryVO>> queryGalleryList(@Valid @RequestBody GalleryQuery query) {
        return Result.success(galleryService.queryGalleryList(query));
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<GalleryVO>> getGalleryByPage(@Valid @RequestBody GalleryQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        PageInfo<GalleryVO> page = galleryService.queryGalleryByPage(query);
        List<GalleryVO> galleryList = CommonUtil.listConverter(page.getList(), this::filterParams);
        page.setList(galleryList);

        return Result.success(page);
    }

    @PostMapping("/upload")
    public Result<JSONObject> galleryUpload(@Valid @ModelAttribute GalleryUploadRequest request) {
        // 参数检查
        GalleryTypeEnum type = GalleryTypeEnum.getByCode(request.getType());
        AssertUtil.assertNotNull(type, ResultCode.BIZ_FAIL, "类型解析失败");
        GallerySubTypeEnum subType = GallerySubTypeEnum.getByCode(request.getSubType());
        AssertUtil.assertNotNull(subType, ResultCode.BIZ_FAIL, "类型解析失败");
        ModelTypeEnum belong = ModelTypeEnum.getByCode(request.getBelong());
        AssertUtil.assertNotNull(belong, ResultCode.BIZ_FAIL, "类型解析失败");

        return Result.success(galleryService.galleryUpload(request));
    }

    private GalleryVO filterParams(GalleryVO data) {
        // 后台用户可查所有字段
        if (OperationContextHolder.isBackRole()) {
            return data;
        }
        data.setId(null);
        data.setBelong(null);
        data.setMd5(null);
        data.setModifyTime(null);
        data.setCreateTime(null);
        return data;
    }
}