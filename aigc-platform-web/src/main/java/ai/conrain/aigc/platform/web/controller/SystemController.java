/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.aliyun.model.StsCredentials;
import ai.conrain.aigc.platform.integration.deerapi.DeerApiService;
import ai.conrain.aigc.platform.integration.deerapi.DeerModelEnum;
import ai.conrain.aigc.platform.integration.gpt.AIModel.GptResponse;
import ai.conrain.aigc.platform.integration.gpt.AIModel.Status;
import ai.conrain.aigc.platform.integration.gpt.OpenAIService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.integration.wechat.TencentCloudService;
import ai.conrain.aigc.platform.integration.wechat.model.QCSVoiceTokenVO;
import ai.conrain.aigc.platform.service.component.MerchantPreferenceService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.component.annotation.Roles;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.ConfigStatusEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.DeviceInfo;
import ai.conrain.aigc.platform.service.model.biz.ExperienceModelOpenDetail;
import ai.conrain.aigc.platform.service.model.biz.MachineRoom;
import ai.conrain.aigc.platform.service.model.biz.MerchantPreferenceDetail;
import ai.conrain.aigc.platform.service.model.common.OssTokenInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.MerchantPreferenceVO;
import ai.conrain.aigc.platform.service.model.vo.SystemConfigVO;
import ai.conrain.aigc.platform.service.model.vo.UserAdditionalConfigVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static ai.conrain.aigc.platform.service.constants.SystemConstants.AUTO_DELIVERY_MERCHANT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.AUTO_TRAIN_MERCHANT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.EXCLUDE_SYSTEM_COLLOCATION;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.HOME_LOGIN_REGISTER_CONFIG;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_MINI_LABEL_MERCHANT_LIST;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_NEW_LABEL_MERCHANT_LIST;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.NIGHTTIME_AUTO_TRAIN_SWITCH;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.NO_CUTOUT_MERCHANT_LIST;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.SEE_ALL_MODELS_AND_HISTORY;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.ADMIN;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DEMO_ACCOUNT;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DISTRIBUTOR;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.MERCHANT;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.OPERATOR;

/**
 * 系统相关控制器
 *
 * <AUTHOR>
 * @version : SystemController.java, v 0.1 2024/2/3 00:08 renxiao.wu Exp $
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/sys")
public class SystemController {
    @Value("${aliyun.oss.bucket}")
    String bucketName;
    @Value("${aliyun.oss.region}")
    String region;
    @Value("${aliyun.oss.bucket}")
    String bucket;
    @Autowired
    private TencentCloudService tencentCloudService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private OssService ossService;
    @Autowired
    private UserService userService;
    @Autowired
    private MerchantPreferenceService merchantPreferenceService;
    @Autowired
    private OpenAIService openAIService;
    @Autowired
    private DeerApiService deerApiService;

    @GetMapping("/voice/fetchToken")
    public Result<QCSVoiceTokenVO> fetchVoiceToken() {
        QCSVoiceTokenVO res = tencentCloudService.getFederationToken();
        if (res != null) {
            return Result.success(res);
        }

        return Result.failedWithMessage(ResultCode.SYS_ERROR, "申请语音token异常");
    }

    @GetMapping("/oss/fetchToken")
    public Result<OssTokenInfo> fetchOssToken() {
        StsCredentials res = ossService.getStsCredentials(30 * 60L, false);
        if (res != null) {
            return Result.success(new OssTokenInfo(res, bucketName, region));
        }

        return Result.failedWithMessage(ResultCode.SYS_ERROR, "申请临时token异常");
    }

    @PostMapping("/oss/download")
    public ResponseEntity<Resource> downloadOss(@NotNull @RequestParam("url") String url) throws MalformedURLException {
        String imageUrl = CommonUtil.getFilePathAndNameFromURL(url);
        String originImageName = CommonUtil.getFileNameWithoutExtension(imageUrl);
        String tmpUrl = ossService.downloadFile(imageUrl, "/tmp/", originImageName);
        log.info(tmpUrl);

        Path path = Paths.get(tmpUrl); // 假设 imageUrl 是服务器上的一个本地路径
        Resource resource = new UrlResource(path.toUri());

        return ResponseEntity.ok().header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*").header(
            HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"").header(
            HttpHeaders.CONTENT_TYPE, MediaType.IMAGE_PNG_VALUE).body(resource);
    }

    @GetMapping("/all")
    public Result<List<SystemConfigVO>> queryAll() {
        List<SystemConfigVO> all = systemConfigService.queryAll();
        //按修改时间倒序
        all.sort((o1, o2) -> o2.getModifyTime().compareTo(o1.getModifyTime()));
        return Result.success(all);
    }

    @PostMapping("/update")
    public Result<?> update(@Valid @RequestBody SystemConfigVO request) {
        AssertUtil.assertTrue(request.getId() != null || StringUtils.isNotBlank(request.getConfKey()),
            "id或key不能为空");

        SystemConfigVO config = null;
        if (request.getId() != null) {
            config = systemConfigService.selectById(request.getId());
        } else {
            config = systemConfigService.queryByKey(request.getConfKey());
        }

        config.setConfKey(request.getConfKey());
        if (StringUtils.isNotBlank(request.getMemo())) {
            config.setMemo(request.getMemo());
        }
        config.setConfValue(request.getConfValue());
        systemConfigService.updateById(config);
        return Result.success();
    }

    @PostMapping("/add")
    public Result<?> add(@Valid @RequestBody SystemConfigVO request) {
        request.setStatus(ConfigStatusEnum.ACTIVE);
        request.setOperatorId(OperationContextHolder.getOperatorUserId());
        request.setEffectTime(new Date());
        systemConfigService.insert(request);
        return Result.success();
    }

    @PostMapping("/delete")
    public Result<?> delete(@NotNull @JsonArg Integer id) {
        systemConfigService.deleteById(id);
        return Result.success();
    }

    @GetMapping("/queryOperators")
    public Result<JSONArray> queryOperators() {
        String value = systemConfigService.queryValueByKey(SystemConstants.OPERATOR_CONFIG);
        JSONArray res = JSONArray.parseArray(value);
        return Result.success(res);
    }

    @GetMapping("/queryModelReplaceKey")
    public Result<String> queryModelReplaceKey() {
        String value = systemConfigService.queryValueByKey(SystemConstants.NEED_REPLACE_MODEL_KEYS);
        return Result.success(value);
    }

    @GetMapping("/canPublishComfyuiWorkflow")
    public Result<Boolean> canPublishComfyuiWorkflow() {
        if (EnvUtil.isProdEnv()) {
            List<Integer> list = Arrays.asList(100009, 100158);
            if (list.contains(OperationContextHolder.getOperatorUserId())) {
                return Result.success(true);
            }
            return Result.success(false);
        } else {
            return Result.success(true);
        }
    }

    @PostMapping("/updateMerchantPreference")
    public Result<?> updateMerchantPreference(@Valid @RequestBody MerchantPreferenceDetail preference) {
        AssertUtil.assertNotNull(preference.getUserId(), "userId不能为空");
        merchantPreferenceService.updateMerchantPreference(preference);
        return Result.success();
    }

    @PostMapping("/queryMerchantPreference")
    public Result<MerchantPreferenceDetail> queryMerchantPreference(@JsonArg @NotNull Integer userId) {
        return Result.success(merchantPreferenceService.queryDetailByUserId(userId));
    }

    @GetMapping("/queryAllMerchantPreference")
    public Result<List<MerchantPreferenceDetail>> queryAllMerchantPreference() {
        return Result.success(merchantPreferenceService.queryAllMerchantPreference());
    }

    /**
     * 查询首页登录注册配置信息
     *
     * @return 配置信息JSON对象，如果配置不存在或格式无效则返回空对象
     */
    @GetMapping("/queryHomeLoginRegisterConfig")
    public Result<JSONObject> queryHomeLoginRegisterConfig() {
        try {
            String configValue = systemConfigService.queryValueByKey(HOME_LOGIN_REGISTER_CONFIG);
            if (StringUtils.isBlank(configValue)) {
                return Result.success(new JSONObject());
            }

            if (!CommonUtil.isValidJson(configValue)) {
                log.warn("首页登录注册配置格式无效: {}", configValue);
                return Result.success(new JSONObject());
            }

            return Result.success(JSONObject.parseObject(configValue));
        } catch (Exception e) {
            log.error("查询首页登录注册配置异常", e);
            return Result.success(new JSONObject());
        }
    }

    @GetMapping("/queryCreativePreference")
    public Result<List<MerchantPreferenceVO>> queryCreativePreference() {
        return Result.success(
            merchantPreferenceService.queryCreativePreference(OperationContextHolder.getMasterUserId()));
    }

    @GetMapping("/queryMerchantConfigs")
    public Result<?> queryMerchantConfigs() {
        Map<String, Object> result = new HashMap<>();

        result.put("autoTrainMerchants", getUsersByArrayKey(AUTO_TRAIN_MERCHANT));
        result.put("autoDeliveryMerchants", getUsersByArrayKey(AUTO_DELIVERY_MERCHANT));
        result.put("excludeSystemCollocation", getUsersByArrayKey(EXCLUDE_SYSTEM_COLLOCATION));
        result.put(SEE_ALL_MODELS_AND_HISTORY, getUsersByArrayKey(SEE_ALL_MODELS_AND_HISTORY));
        result.put(NIGHTTIME_AUTO_TRAIN_SWITCH, systemConfigService.queryValueByKey(NIGHTTIME_AUTO_TRAIN_SWITCH));
        result.put(NO_CUTOUT_MERCHANT_LIST, getUsersByArrayKey(NO_CUTOUT_MERCHANT_LIST));
        result.put(LORA_NEW_LABEL_MERCHANT_LIST, getUsersByArrayKey(LORA_NEW_LABEL_MERCHANT_LIST));
        result.put(LORA_MINI_LABEL_MERCHANT_LIST, getUsersByArrayKey(LORA_MINI_LABEL_MERCHANT_LIST));

        return Result.success(result);
    }

    @PostMapping("/queryConfigByKeys")
    public Result<?> queryConfigByKeys(@JsonArg @NotNull String[] keys) {
        Map<String, Object> result = new HashMap<>();
        for (String key : keys) {
            addAndFormatData(key, result);
        }
        return Result.success(result);
    }

    @PostMapping("/checkCanShowProportion")
    public Result<Boolean> checkCanShowProportion(@JsonArg @NotBlank String proportionType) {
        boolean canShow = false;
        if (OperationContextHolder.getRoleType() == RoleTypeEnum.MERCHANT) {
            //{"P_1620_2100":[100023]}
            JSONObject cfg = systemConfigService.queryJsonValue(SystemConstants.SHOW_HQ_RESOLUTION_WHILTELIST);
            if (cfg != null && cfg.containsKey(proportionType) && cfg.getJSONArray(proportionType).contains(
                OperationContextHolder.getMasterUserId())) {
                canShow = true;
            }
        }

        return Result.success(canShow);
    }

    /**
     * 获取菜单权限 ADMIN_MENUS_CFG
     * {
     * "/business-mng": [
     * 100000
     * ],
     * "/system": [
     * 100000
     * ]
     * }
     */
    @GetMapping("/queryMenusCfg")
    public Result<String> queryMenusCfg() {
        if (OperationContextHolder.getRoleType() != RoleTypeEnum.ADMIN) {
            return Result.success();
        }

        String cfg = systemConfigService.queryValueByKey(SystemConstants.ADMIN_MENUS_CFG);
        if (cfg != null && CommonUtil.isValidJson(cfg)) {
            return Result.success(cfg);
        } else {
            return Result.success();
        }
    }

    @GetMapping("/queryAllExperienceModelOpenCfg")
    public Result<?> queryAllExperienceModelOpenCfg() {
        JSONArray result = systemConfigService.queryJsonArrValue(SystemConstants.EXPERIENCE_MODEL_OPEN_CFG);
        return Result.success(result);
    }

    // 获取图片案例同步配置
    @GetMapping("/queryImageCaseSyncConfig")
    public Result<?> queryImageCaseSyncConfig() {
        String imageCaseSyncConfig = systemConfigService.queryValueByKey(SystemConstants.IMAGE_CASE_SYNC_CONFIG);
        if (imageCaseSyncConfig != null && CommonUtil.isValidJson(imageCaseSyncConfig)) {
            return Result.success(imageCaseSyncConfig);
        } else {
            return Result.success();
        }
    }

    @PostMapping("/queryExperienceModelOpenCfgById")
    public Result<?> queryExperienceModelOpenCfgById(@JsonArg @NotNull Integer id) {
        JSONArray cfg = systemConfigService.queryJsonArrValue(SystemConstants.EXPERIENCE_MODEL_OPEN_CFG);
        if (CollectionUtils.isNotEmpty(cfg)) {
            Object object = cfg.stream().filter(item -> ((JSONObject)item).getInteger("id").equals(id)).findFirst()
                .orElse(null);

            return Result.success(object);
        }
        return Result.success();
    }

    @GetMapping("/queryLoraVipCfg")
    public Result<JSONArray> queryLoraVipCfg() {
        JSONArray cfg = systemConfigService.queryJsonArrValue(SystemConstants.LORA_VIP);
        return Result.success(cfg);
    }

    /**
     * 查询用户额外配置
     */
    @GetMapping("/queryAdditionalCustomerRequirements")
    public Result<UserAdditionalConfigVO> queryAdditionalCustomerRequirements() {
        UserAdditionalConfigVO userAdditionalConfigVO = systemConfigService.queryAdditionalCustomerRequirements();
        return Result.success(userAdditionalConfigVO);
    }

    @PostMapping("/modifyExperienceModelOpenCfg")
    public Result<?> queryExperienceModelOpenCfgById(@Valid @RequestBody ExperienceModelOpenDetail detail) {
        SystemConfigVO config = systemConfigService.queryByKey(SystemConstants.EXPERIENCE_MODEL_OPEN_CFG);
        AssertUtil.assertNotNull(config, "配置不存在");

        JSONArray cfg = JSONArray.parseArray(config.getConfValue());

        Integer id = detail.getId();

        //先删除原来的数据，如果有的话
        cfg.removeIf(item -> ((JSONObject)item).getInteger("id").equals(id));
        cfg.add(detail);

        config.setConfValue(cfg.toJSONString());
        systemConfigService.updateById(config);

        return Result.success();
    }

    @PostMapping("/modifyDeviceInfo")
    public Result<?> modifyDeviceInfo(@RequestBody DeviceInfo deviceInfo) {
        systemConfigService.updateDeviceInfo(deviceInfo.getRooms());
        return Result.success();
    }

    @GetMapping("/queryDeviceInfo")
    public Result<List<MachineRoom>> queryDeviceInfo() {
        return Result.success(systemConfigService.queryDeviceInfo());
    }

    @PostMapping("/callLLM")
    public Result<?> callLLM(@JsonArg @NotBlank String prompt, @JsonArg String model, @JsonArg JSONArray imgUrls) {
        List<String> images = CollectionUtils.isNotEmpty(imgUrls) ? imgUrls.toJavaList(String.class) : null;
        DeerModelEnum modelEnum = DeerModelEnum.getByCode(model, DeerModelEnum.GPT_4_1);
        GptResponse res = null;
        if (modelEnum == DeerModelEnum.GPT_4_1) {
            res = openAIService.requestGpt(prompt, images);
        } else {
            res = deerApiService.call(modelEnum, prompt, images);
        }

        if (res.getStatus() != Status.OK) {
            return Result.failedWithMessage(ResultCode.BIZ_FAIL, res.getStatus().toString() + ": " + res.getText());
        }

        return Result.success(CommonUtil.parseJsonStringFromGpt(res.getText()));
    }

    @PostMapping("/identifyClothing")
    @Roles({ADMIN, OPERATOR, DEMO_ACCOUNT, MERCHANT, DISTRIBUTOR})
    public Result<?> identifyClothing(@JsonArg @NotBlank String imageUrl) {
        String prompt = systemConfigService.queryValueByKey(SystemConstants.CLOTHING_IDENTIFY_PROMPT);
        GptResponse res = openAIService.requestGpt(prompt, Collections.singletonList(imageUrl));

        if (res.getStatus() != Status.OK) {
            return Result.failedWithMessage(ResultCode.BIZ_FAIL, res.getStatus().toString() + ": " + res.getText());
        }

        return Result.success(CommonUtil.parseJsonStringFromGpt(res.getText()));
    }

    /**
     * 格式化并添加到结果集中
     *
     * @param key    关键字
     * @param result 结果集
     */
    private void addAndFormatData(String key, Map<String, Object> result) {
        String value = null;
        if (StringUtils.contains(key, ".")) {
            String[] split = StringUtils.split(key, ".");
            JSONObject root = systemConfigService.queryJsonValue(split[0]);
            if (root != null && root.get(split[1]) != null) {
                value = root.get(split[1]).toString();
            }
        } else {
            value = systemConfigService.queryValueByKey(key);
        }

        if (CommonUtil.isValidJsonArray(value) || CommonUtil.isValidJson(value)) {
            result.put(key, value);
        } else {
            value = CommonUtil.unescapeLineBreak(value);
            value = ComfyUIUtils.parseParams(value);
            value = StringUtils.isBlank(value) ? "" : value;
            result.put(key, value);
        }
    }

    /**
     * 从jsonArray中解析出包含名称和id的用户列表
     *
     * @param key 关键字
     * @return 结果
     */
    private List<UserVO> getUsersByArrayKey(String key) {
        JSONArray autoTrainMerchants = systemConfigService.queryJsonArrValue(key);

        if (CollectionUtils.isEmpty(autoTrainMerchants)) {
            return null;
        }

        List<Integer> ids = autoTrainMerchants.stream().map(item -> Integer.parseInt(item.toString())).collect(
            Collectors.toList());

        List<UserVO> users = userService.batchQueryById(ids);

        //返回的user信息，只保留id和nickName
        users = users.stream().map(user -> {
            UserVO userVO = new UserVO();
            userVO.setId(user.getId());
            userVO.setNickName(user.getNickName());
            return userVO;
        }).collect(Collectors.toList());

        return users;
    }
}
