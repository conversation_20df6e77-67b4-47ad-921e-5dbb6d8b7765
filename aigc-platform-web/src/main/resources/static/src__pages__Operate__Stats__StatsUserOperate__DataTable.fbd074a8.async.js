"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[939],{32412:function(O,o,e){e.r(o);var C=e(75271),u=e(55440),s=e(15354),i=e(16483),c=e.n(i),r=e(52676),E=function(a){var M=a.data,D=a.loading,x=a.sortField,P=a.sortOrder,m=a.onTableChange,f=a.statsType,Y=function(t,l){if(!t)return"-";try{var d=c()(t);if(l==="WEEKLY"){var T=d.add(6,"day");return"".concat(d.format("YYYY-MM-DD")," \u81F3 ").concat(T.format("YYYY-MM-DD"))}else if(l==="MONTHLY"){var h=d.endOf("month");return"".concat(d.format("YYYY-MM-DD")," \u81F3 ").concat(h.format("YYYY-MM-DD"))}else return d.format("YYYY-MM-DD")}catch(y){return console.error("\u65E5\u671F\u683C\u5F0F\u5316\u9519\u8BEF:",y),t||"-"}},I=function(t){return t?t==="MASTER"?"\u4E3B\u8D26\u6237":"\u5B50\u8D26\u53F7":"-"};return(0,r.jsx)("div",{className:"operate-stats-table-container",children:(0,r.jsx)(u.Z,{loading:D,dataSource:M,rowKey:"id",pagination:!1,onChange:m,scroll:{x:"max-content",y:450},columns:[{title:"\u7528\u6237ID",dataIndex:"userId",key:"userId",width:100},{title:"\u7528\u6237\u7C7B\u578B",dataIndex:"userType",key:"userType",width:100,render:function(t){return I(t)}},{title:"\u670D\u88C5ID",dataIndex:"materialId",key:"materialId",width:100},{title:"\u51FA\u56FE\u91CF",dataIndex:"createCount",key:"createCount",width:120,sorter:!0,render:function(t){return(0,r.jsx)("span",{className:"operate-stats-metric-create",children:t||0})}},{title:"\u4E0B\u8F7D\u91CF",dataIndex:"downloadCount",key:"downloadCount",width:120,sorter:!0,render:function(t){return(0,r.jsx)("span",{className:"operate-stats-metric-download",children:t||0})}},{title:"\u7EDF\u8BA1\u65E5\u671F",dataIndex:"statsDate",key:"statsDate",width:180,render:function(t){return Y(t,f)}}],locale:{emptyText:(0,r.jsx)(s.Z,{image:s.Z.PRESENTED_IMAGE_SIMPLE,description:"\u6682\u65E0\u6570\u636E"})}})})};o.default=E}}]);
