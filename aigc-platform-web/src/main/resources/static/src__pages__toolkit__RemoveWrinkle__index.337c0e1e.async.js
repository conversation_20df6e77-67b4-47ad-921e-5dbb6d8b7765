"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[60],{33684:function(Pe,O,e){e.r(O);var F=e(90228),m=e.n(F),k=e(87999),P=e.n(k),$=e(48305),r=e.n($),z=e(83223),d=e(11377),G=e(16930),H=e(45046),J=e(78224),a=e(75271),g=e(96535),Q=e(8560),X=e(25351),Y=e(33565),w=e(31537),q=e(77762),ee=e(24573),t=e(52676),te=function(ae){var _e=ae.useSliderValue,se=(0,a.useState)(""),D=r()(se,2),o=D[0],I=D[1],ne=(0,a.useState)(!1),S=r()(ne,2),M=S[0],f=S[1],re=(0,a.useState)(null),h=r()(re,2),De=h[0],le=h[1],oe=(0,a.useState)(null),C=r()(oe,2),l=C[0],ue=C[1],ie=(0,a.useState)(!1),R=r()(ie,2),Ie=R[0],T=R[1],me=(0,a.useState)(),U=r()(me,2),u=U[0],W=U[1],B=(0,a.useRef)(null),ce=(0,a.useRef)(null),de=(0,a.useState)(!1),K=r()(de,2),L=K[0],j=K[1],fe=(0,a.useState)(!1),A=r()(fe,2),c=A[0],v=A[1],ve=(0,a.useState)("upload"),y=r()(ve,2),N=y[0],V=y[1],x=r()(_e,2),b=x[0],Ee=x[1];(0,a.useEffect)(function(){var s=localStorage.getItem("imageUrl");s&&(I(s),V("history"));var n=localStorage.getItem("userInfo");if(n){var _=JSON.parse(n);le(_)}return Z(),function(){localStorage.removeItem("imageUrl"),localStorage.removeItem("modelId")}},[]),(0,a.useEffect)(function(){j(!!o)},[o]),(0,a.useEffect)(function(){var s=null;return u&&u.id&&u.status!=="FINISHED"&&(s=setInterval(P()(m()().mark(function n(){var _;return m()().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,(0,g.DI)(u.id);case 2:_=i.sent,_&&(W(_),_.status==="FINISHED"&&(clearInterval(s),T(!1)));case 4:case"end":return i.stop()}},n)})),2e3)),function(){s&&clearInterval(s)}},[u]);function Z(){return E.apply(this,arguments)}function E(){return E=P()(m()().mark(function s(){return m()().wrap(function(_){for(;;)switch(_.prev=_.next){case 0:(0,X.m8)("REMOVE_WRINKLE",1).then(function(p){p&&ue(p)});case 1:case"end":return _.stop()}},s)})),E.apply(this,arguments)}var pe=function(){if(!o){d.ZP.error("\u8BF7\u5148\u4E0A\u4F20\u56FE\u7247");return}if(N==="upload"&&l&&l.needTopup){f(!0);return}c||(v(!0),(0,g.QX)({originImage:o}).then(function(n){n&&(d.ZP.success("\u63D0\u4EA4\u6210\u529F"),setTimeout(function(){var _;T(!0),W(n),(_=B.current)===null||_===void 0||_.refresh()},0)),v(!1)}).catch(function(n){d.ZP.error(n.message||"\u56FE\u7247\u5904\u7406\u5931\u8D25"),v(!1)}))},Oe=function(n){I(n)};return(0,t.jsxs)(z._z,{children:[(0,t.jsxs)(G.Z,{className:"toolkit-row-container",children:[(0,t.jsxs)("div",{className:"toolkit-work-block",children:[(0,t.jsx)(q.Z,{className:"redraw-image-upload",title:"\u5F85\u4FEE\u590D\u56FE\u7247",image:o,setImageSource:V,onImageChange:Oe,historyFree:!0,uploadCost:.4}),(0,t.jsxs)("div",{className:"toolkit-number-input-container",children:[(0,t.jsx)("div",{className:"text16 font-pf color-n weight",children:"\u751F\u6210\u6570\u91CF"}),(0,t.jsx)(H.Z,{value:1,disabled:!0})]})]}),(0,t.jsx)("div",{ref:ce,className:"toolkit-output-block",style:{width:"calc( 58% * ".concat(b/12,")"),maxWidth:"100%"},children:(0,t.jsx)(w.default,{sliderValue:b,changeSliderValue:Ee,types:["REMOVE_WRINKLE"],ref:B,pollingTimeout:2e3})})]}),(0,t.jsx)("footer",{className:"toolkit-footer",children:(0,t.jsxs)("div",{className:"toolkit-footer-content",children:[L&&N==="upload"&&(0,t.jsx)(Q.Z,{creativeType:"REMOVE_WRINKLE",predictVO:l}),(0,t.jsxs)(J.ZP,{type:"primary",className:"create-btn",disabled:!L||c||(l==null?void 0:l.needTopup),icon:c?(0,t.jsx)(ee.Z,{style:{fontSize:16,color:"fff"}}):"",onClick:pe,children:[" ",l!=null&&l.needTopup?"\u4F59\u989D\u4E0D\u8DB3\uFF0C\u53BB\u5145\u503C":c?"\u751F\u6210\u4E2D":"\u5F00\u59CB\u53BB\u76B1"]})]})}),M&&(0,t.jsx)(Y.default,{visible:M,onClose:function(){return f(!1)},onPaySuccess:function(){f(!1),Z()}})]})};O.default=te}}]);
