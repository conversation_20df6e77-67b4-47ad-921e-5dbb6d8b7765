"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[60],{96269:function(Pe,O,e){e.r(O);var Z=e(90228),c=e.n(Z),w=e(87999),P=e.n(w),F=e(48305),n=e.n(F),$=e(20865),m=e(91380),z=e(6881),G=e(49456),H=e(1980),a=e(75271),g=e(30575),J=e(30996),Q=e(58191),X=e(33964),Y=e(65760),q=e(35395),ee=e(79312),t=e(52676),te=function(ae){var _e=ae.useSliderValue,se=(0,a.useState)(""),I=n()(se,2),l=I[0],S=I[1],re=(0,a.useState)(!1),M=n()(re,2),h=M[0],f=M[1],ne=(0,a.useState)(null),D=n()(ne,2),Ie=D[0],oe=D[1],le=(0,a.useState)(null),C=n()(le,2),o=C[0],ue=C[1],ie=(0,a.useState)(!1),R=n()(ie,2),Se=R[0],T=R[1],ce=(0,a.useState)(),U=n()(ce,2),u=U[0],W=U[1],B=(0,a.useRef)(null),de=(0,a.useRef)(null),me=(0,a.useState)(!1),K=n()(me,2),L=K[0],j=K[1],fe=(0,a.useState)(!1),A=n()(fe,2),d=A[0],E=A[1],Ee=(0,a.useState)("upload"),x=n()(Ee,2),y=x[0],N=x[1],V=n()(_e,2),b=V[0],ve=V[1];(0,a.useEffect)(function(){var s=localStorage.getItem("imageUrl");s&&(S(s),N("history"));var r=localStorage.getItem("userInfo");if(r){var _=JSON.parse(r);oe(_)}return k(),function(){localStorage.removeItem("imageUrl"),localStorage.removeItem("modelId")}},[]),(0,a.useEffect)(function(){j(!!l)},[l]),(0,a.useEffect)(function(){var s=null;return u&&u.id&&u.status!=="FINISHED"&&(s=setInterval(P()(c()().mark(function r(){var _;return c()().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,(0,g.DI)(u.id);case 2:_=i.sent,_&&(W(_),_.status==="FINISHED"&&(clearInterval(s),T(!1)));case 4:case"end":return i.stop()}},r)})),2e3)),function(){s&&clearInterval(s)}},[u]);function k(){return v.apply(this,arguments)}function v(){return v=P()(c()().mark(function s(){return c()().wrap(function(_){for(;;)switch(_.prev=_.next){case 0:(0,Q.m8)("REMOVE_WRINKLE",1).then(function(p){p&&ue(p)});case 1:case"end":return _.stop()}},s)})),v.apply(this,arguments)}var pe=function(){if(!l){m.ZP.error("\u8BF7\u5148\u4E0A\u4F20\u56FE\u7247");return}if(y==="upload"&&o&&o.needTopup){f(!0);return}d||(E(!0),(0,g.QX)({originImage:l}).then(function(r){r&&(m.ZP.success("\u63D0\u4EA4\u6210\u529F"),setTimeout(function(){var _;T(!0),W(r),(_=B.current)===null||_===void 0||_.refresh()},0)),E(!1)}).catch(function(r){m.ZP.error(r.message||"\u56FE\u7247\u5904\u7406\u5931\u8D25"),E(!1)}))},Oe=function(r){S(r)};return(0,t.jsxs)($._z,{children:[(0,t.jsxs)(z.Z,{className:"toolkit-row-container",children:[(0,t.jsxs)("div",{className:"toolkit-work-block",children:[(0,t.jsx)(q.Z,{className:"redraw-image-upload",title:"\u5F85\u4FEE\u590D\u56FE\u7247",image:l,setImageSource:N,onImageChange:Oe,historyFree:!0,uploadCost:.4}),(0,t.jsxs)("div",{className:"toolkit-number-input-container",children:[(0,t.jsx)("div",{className:"text16 font-pf color-n weight",children:"\u751F\u6210\u6570\u91CF"}),(0,t.jsx)(G.Z,{value:1,disabled:!0})]})]}),(0,t.jsx)("div",{ref:de,className:"toolkit-output-block",style:{width:"calc( 58% * ".concat(b/12,")"),maxWidth:"100%"},children:(0,t.jsx)(Y.default,{sliderValue:b,changeSliderValue:ve,types:["REMOVE_WRINKLE"],ref:B,pollingTimeout:2e3})})]}),(0,t.jsx)("footer",{className:"toolkit-footer",children:(0,t.jsxs)("div",{className:"toolkit-footer-content",children:[L&&y==="upload"&&(0,t.jsx)(J.Z,{creativeType:"REMOVE_WRINKLE",predictVO:o}),(0,t.jsxs)(H.ZP,{type:"primary",className:"create-btn",disabled:!L||d||(o==null?void 0:o.needTopup),icon:d?(0,t.jsx)(ee.Z,{style:{fontSize:16,color:"fff"}}):"",onClick:pe,children:[" ",o!=null&&o.needTopup?"\u4F59\u989D\u4E0D\u8DB3\uFF0C\u53BB\u5145\u503C":d?"\u751F\u6210\u4E2D":"\u5F00\u59CB\u53BB\u76B1"]})]})}),h&&(0,t.jsx)(X.default,{visible:h,onClose:function(){return f(!1)},onPaySuccess:function(){f(!1),k()}})]})};O.default=te}}]);
